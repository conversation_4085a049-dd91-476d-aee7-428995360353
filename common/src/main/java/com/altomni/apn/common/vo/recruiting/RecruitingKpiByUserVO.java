package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiByUserVO extends RecruitingKpiCommonVO {

    private List<RecruitingKpiByUserVO> children;

    private Long callNoteNum;

    private String callNoteIds;

    private Long emailNoteNum;

    private String emailNoteIds;

    private Long personNoteNum;

    private String personNoteIds;

    private Long videoNoteNum;

    private String videoNoteIds;

    private Long otherNoteNum;

    private Long iciNum;

    private Long upgradeToClient;

    private Long createdCompaniesNum;

    private String otherNoteIds;

    private Long pipelineNoteNum;

    private String pipelineNoteIds;

    private String submitToJobNoteIds;

    private String submitToClientNoteIds;

    private String interviewNoteIds;

    private String offerNoteIds;

    private String offerAcceptNoteIds;

    private String onboardNoteIds;

    private String eliminateNoteIds;

    private Long apnProNoteNum;

    private String apnProNoteIds;

    private Long noteCount;

    private String uniqueTalentIds;

}
