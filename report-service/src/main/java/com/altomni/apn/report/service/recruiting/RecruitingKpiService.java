package com.altomni.apn.report.service.recruiting;

import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface RecruitingKpiService {

    List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto);

    List<RecruitingKpiByUserVO> drillRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto);

    RecruitingKpiByUserVO searchRecruitingKpiTotalByUser(RecruitingKpiReportSearchDto searchDto);

    List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserForE5(RecruitingKpiReportSearchDto searchDto);

    List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto);

    RecruitingKpiByCompanyVO searchRecruitingKpiTotalByCompany(RecruitingKpiReportSearchDto searchDto);

    void exportRecruitingKpiReportByUserExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response);

    void exportRecruitingKpiReportByCompanyExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response);

}
