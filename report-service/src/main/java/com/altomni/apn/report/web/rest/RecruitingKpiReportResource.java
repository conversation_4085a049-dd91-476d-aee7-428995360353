package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchKpiCompanyDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.report.config.StarRocksProperties;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.service.recruiting.RecruitingKpiByCompanyService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiByUserService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/recruiting")
public class RecruitingKpiReportResource {

    @Resource
    private RecruitingKpiService recruitingKpiService;

    @Resource(name = "recruitingKpiServiceV2")
    private RecruitingKpiService recruitingKpiServiceV2;

    @Resource
    protected StarRocksProperties starRocksProperties;

    @Resource
    private RecruitingKpiByUserService recruitingKpiByUserService;

    @Resource
    private RecruitingKpiByCompanyService recruitingKpiByCompanyService;


    @PostMapping("/kpi-report-by-user")
    public ResponseEntity<List<RecruitingKpiByUserVO>> searchRecruitingKpiReportByUser(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        if (starRocksProperties.isEnabled()) {
            return ResponseEntity.ok(recruitingKpiServiceV2.searchRecruitingKpiReportByUser(searchDto));
        } else {
            return ResponseEntity.ok(recruitingKpiService.searchRecruitingKpiReportByUser(searchDto));
        }
    }

    @PostMapping("/kpi-report-by-user/drill-down")
    public ResponseEntity<List<RecruitingKpiByUserVO>> drillDownRecruitingKpiReportByUser(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        if (searchDto.getParentId() == null) {
            throw new CustomParameterizedException("parentId is null");
        }
        return ResponseEntity.ok(recruitingKpiServiceV2.drillRecruitingKpiReportByUser(searchDto));
    }

    @PostMapping("/kpi-report-by-user-count")
    public ResponseEntity<RecruitingKpiByUserVO> countRecruitingKpiReportByUser(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        return ResponseEntity.ok(recruitingKpiServiceV2.searchRecruitingKpiTotalByUser(searchDto));
    }

    @PostMapping("/kpi-report-by-user-excel")
    public void exportRecruitingKpiReportByUserExcel(@RequestBody RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiServiceV2.exportRecruitingKpiReportByUserExcel(searchDto, response);
    }

    @PostMapping("/kpi-report-by-company")
    public ResponseEntity<List<RecruitingKpiByCompanyVO>> searchRecruitingKpiReportByCompany(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        if (starRocksProperties.isEnabled()) {
            return ResponseEntity.ok(recruitingKpiServiceV2.searchRecruitingKpiReportByCompany(searchDto));
        } else {
            return ResponseEntity.ok(recruitingKpiService.searchRecruitingKpiReportByCompany(searchDto));
        }
    }

    @PostMapping("/kpi-report-by-company/drill-down")
    public ResponseEntity<List<RecruitingKpiByCompanyVO>> drillDownRecruitingKpiReportByCompany(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        if (CollUtil.isEmpty(searchDto.getCompanyIdList()) || searchDto.getCompanyIdList().size()>1) {
            throw new CustomParameterizedException("parentId is null");
        }
        SearchKpiCompanyDto company = searchDto.getCompany();
        if (null == company) {
            company = new SearchKpiCompanyDto();
        }
        company.setIdList(searchDto.getCompanyIdList());
        searchDto.setCompany(company);
        //维度为Company 和job
        searchDto.setGroupByFieldList(Arrays.asList(RecruitingKpiGroupByFieldType.COMPANY,RecruitingKpiGroupByFieldType.JOB));
        if (starRocksProperties.isEnabled()) {
            return ResponseEntity.ok(recruitingKpiServiceV2.searchRecruitingKpiReportByCompany(searchDto));
        } else {
            return ResponseEntity.ok(recruitingKpiService.searchRecruitingKpiReportByCompany(searchDto));
        }
    }

    @PostMapping("/kpi-report-by-company-count")
    public ResponseEntity<RecruitingKpiByCompanyVO> countRecruitingKpiReportByCompany(@RequestBody RecruitingKpiReportSearchDto searchDto) {
        if (starRocksProperties.isEnabled()) {
            return ResponseEntity.ok(recruitingKpiServiceV2.searchRecruitingKpiTotalByCompany(searchDto));
        } else {
            return ResponseEntity.ok(recruitingKpiService.searchRecruitingKpiTotalByCompany(searchDto));
        }
    }

    @PostMapping("/kpi-report-by-company-excel")
    public void exportRecruitingKpiReportByCompanyExcel(@RequestBody RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiServiceV2.exportRecruitingKpiReportByCompanyExcel(searchDto, response);
    }

    @PostMapping("/kpi/job-detail-list")
    public ResponseEntity<List<RecruitingKpiJobDetailVO>> searchJobDetailList(@RequestBody RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchJobDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiJobDetailVO> page = recruitingKpiByUserService.searchJobDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchJobDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/job-detail-list-excel")
    public void exportJobDetailList(@RequestBody RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportJobDetailList(searchDto, response);
    }

    @PostMapping("/kpi/talent-detail-list")
    public ResponseEntity<List<RecruitingKpiTalentDetailVO>> searchTalentDetailList(@RequestBody RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchTalentDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiTalentDetailVO> page = recruitingKpiByUserService.searchTalentDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchTalentDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/talent-detail-list-excel")
    public void exportTalentDetailList(@RequestBody RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportTalentDetailList(searchDto, response);
    }

    @PostMapping("/kpi/application-detail-list")
    public ResponseEntity<List<? extends RecruitingKpiApplicationBaseDetailVO>> searchApplicationDetailList(@RequestBody RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchApplicationDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<? extends RecruitingKpiApplicationBaseDetailVO> page = recruitingKpiByUserService.searchApplicationDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchApplicationDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/application-detail-list-excel")
    public void exportApplicationDetailList(@RequestBody RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportApplicationDetailList(searchDto, response, null);
    }

    @PostMapping("/kpi/talent-note-detail-list")
    public ResponseEntity<List<RecruitingKpiTalentNoteDetailVO>> searchTalentNoteDetailList(@RequestBody RecruitingKpiTalentNoteDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchTalentNoteDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiTalentNoteDetailVO> page = recruitingKpiByUserService.searchTalentNoteDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchTalentNoteDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/talent-note-detail-list-excel")
    public void exportTalentNoteDetailList(@RequestBody RecruitingKpiTalentNoteDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportTalentNoteDetailList(searchDto, response, null, false);
    }

    @PostMapping("/kpi/application-note-detail-list")
    public ResponseEntity<List<RecruitingKpiApplicationNoteDetailVO>> searchApplicationNoteDetailList(@RequestBody RecruitingKpiApplicationNoteDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchApplicationNoteDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiApplicationNoteDetailVO> page = recruitingKpiByUserService.searchApplicationNoteDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchApplicationNoteDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/application-note-detail-list-excel")
    public void exportApplicationNoteDetailList(@RequestBody RecruitingKpiApplicationNoteDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportApplicationNoteDetailList(searchDto, response);
    }

    @PostMapping("/kpi/apn-pro-note-detail-list")
    public ResponseEntity<List<RecruitingKpiApnProNoteDetailVO>> searchApnProNoteDetailList(@RequestBody RecruitingKpiApnProNoteDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchApnProNoteDetailPage by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiApnProNoteDetailVO> page = recruitingKpiByUserService.searchApnProNoteDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchApnProNoteDetailPage by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/apn-pro-note-detail-list-excel")
    public void exportApnProNoteDetailList(@RequestBody RecruitingKpiApnProNoteDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportApnProNoteDetailList(searchDto, response);
    }

    @PostMapping("/kpi/upgrade-to-client-detail-list")
    public ResponseEntity<List<RecruitingKpiUpgradeToClientDetailVO>> searchUpgradeToClientDetailList(@RequestBody RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchUpgradeToClientDetailList by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiUpgradeToClientDetailVO> page = recruitingKpiByUserService.searchUpgradeToClientDetailList(searchDto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchUpgradeToClientDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/upgrade-to-client-detail-list-excel")
    public void exportUpgradeToClientDetailList(@RequestBody RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByUserService.exportUpgradeToClientDetailList(searchDto, response);
    }

    @PostMapping("/kpi/create-company-detail-list")
    public ResponseEntity<List<BDReportKpiUserCompanyDetailVO>> searchCreateCompanyDetailList(@RequestBody RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchCreateCompanyDetailList by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<BDReportKpiUserCompanyDetailVO> page = recruitingKpiByUserService.searchCreateCompanyDetailList(searchDto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchCreateCompanyDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);

    }

    @PostMapping("/kpi/create-company-detail-list-excel")
    public void exportCreateCompanyDetailList(@RequestBody RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response) {
        log.info("[apn @{}] searchCreateCompanyDetailList by user", SecurityUtils.getUserId());
        recruitingKpiByUserService.exportCreateCompanyDetailList(searchDto, response);
    }

    /***
     *  by company detail ---------------------------------------------- 详情
     */
    @PostMapping("/kpi/job-detail-list-by-company")
    public ResponseEntity<List<RecruitingKpiJobDetailVO>> searchJobDetailListByCompany(@RequestBody RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchJobDetailPage by company", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiJobDetailVO> page = recruitingKpiByCompanyService.searchJobDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchJobDetailPage by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/job-detail-list-by-company-excel")
    public void exportJobDetailListByCompany(@RequestBody RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByCompanyService.exportJobDetailList(searchDto, response);
    }

    @PostMapping("/kpi/talent-detail-list-by-company")
    public ResponseEntity<List<RecruitingKpiTalentDetailVO>> searchTalentDetailListByCompany(@RequestBody RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchTalentDetailPage by company", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiTalentDetailVO> page = recruitingKpiByCompanyService.searchTalentDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchTalentDetailPage by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/talent-detail-list-by-company-excel")
    public void exportTalentDetailListByCompany(@RequestBody RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByCompanyService.exportTalentDetailList(searchDto, response);
    }

    @PostMapping("/kpi/application-detail-list-by-company")
    public ResponseEntity<List<? extends RecruitingKpiApplicationBaseDetailVO>> searchApplicationDetailListByCompany(@RequestBody RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchApplicationDetailPage by company", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<? extends RecruitingKpiApplicationBaseDetailVO> page = recruitingKpiByCompanyService.searchApplicationDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchApplicationDetailPage by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/application-detail-list-by-company-excel")
    public void exportApplicationDetailListByCompany(@RequestBody RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByCompanyService.exportApplicationDetailList(searchDto, response);
    }

    @PostMapping("/kpi/job-note-detail-list-by-company")
    public ResponseEntity<List<RecruitingKpiJobNoteDetailVO>> searchJobNoteDetailListByCompany(@RequestBody RecruitingKpiJobNoteDetailSearchDto searchDto, Pageable pageable) {
        log.info("[apn @{}] searchJobNoteDetailPage by company ", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<RecruitingKpiJobNoteDetailVO> page = recruitingKpiByCompanyService.searchJobNoteDetailPage(searchDto, pageable, true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        stopWatch.stop();
        log.info(" searchJobNoteDetailPage by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/job-note-detail-list-by-company-excel")
    public void exportJobNoteDetailListByCompany(@RequestBody RecruitingKpiJobNoteDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByCompanyService.exportJobNoteDetailList(searchDto, response);
    }

    @GetMapping("/kpi/report-country")
    public ResponseEntity<List<String>> getKpiReportCountry() {
        return ResponseEntity.ok(recruitingKpiByCompanyService.getKpiReportCountry());
    }

    @PostMapping("/kpi/company-note-detail-list")
    public ResponseEntity<List<RecruitingKpiCompanyNoteDetailVO>> searchCompanyNoteDetailList(@RequestBody RecruitingKpiCompanyNoteDetailSearchDto searchDto, Pageable pageable) {
        Page<RecruitingKpiCompanyNoteDetailVO> page = recruitingKpiByCompanyService.searchCompanyNoteDetailPage(searchDto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/kpi/company-note-detail-list-excel")
    public void exportCompanyNoteDetailList(@RequestBody RecruitingKpiCompanyNoteDetailSearchDto searchDto, HttpServletResponse response) {
        recruitingKpiByCompanyService.exportCompanyNoteDetailList(searchDto, response);
    }
}
