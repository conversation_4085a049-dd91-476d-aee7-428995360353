package com.altomni.apn.report.service.recruiting.impl.v2;

import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class RecruitingKpiByUserTreeHelper {

    /**
     * 构建KPI报告的树结构
     *
     * @param voList    原始数据列表
     * @param searchDto 搜索条件
     * @return 树结构根节点列表
     */
    public List<RecruitingKpiByUserVO> buildKpiTreeStructure(List<RecruitingKpiByUserVO> voList, RecruitingKpiReportSearchDto searchDto) {
        List<RecruitingKpiByUserVO> treeRootVoList;

        switch (searchDto.getViewType()) {
            case TEAM -> {
                treeRootVoList = buildTeamOnlyTree(voList);
            }
            case TIMELINE -> {
                treeRootVoList = buildTimelineTree(voList);
            }
            default -> throw new CustomParameterizedException("Invalid view type");
        }

        return treeRootVoList;
    }


    /**
     * 构建仅团队维度的树结构
     */
    private List<RecruitingKpiByUserVO> buildTeamOnlyTree(List<RecruitingKpiByUserVO> voList) {
        List<TreeNode> nodeList = voList.stream().map(TreeNode::new).toList();

        Map<Long, List<TreeNode>> childmap = new HashMap<>();
        voList.forEach(vo -> {
            List<TreeNode> children = childmap.computeIfAbsent(vo.getTeamParentId(), k -> new ArrayList<>());
            TreeNode treeNode = new TreeNode(vo);
            if (children.stream().noneMatch(c -> c.getId().equals(treeNode.getId()))) {
                children.add(treeNode);
            }
        });
        Map<Long, TreeNode> idMap = nodeList.stream().collect(Collectors.toMap(TreeNode::getId, Function.identity(), (n1, n2) -> n1));

        // 拿到所有的根节点 id
        Set<Long> allRootIds = TreeNode.findRoots(nodeList);
        // 拿到所有的根节点
        List<TreeNode> rootNodes = allRootIds.stream().map(idMap::get).toList();

        // 递归构建树结构
        for (TreeNode rootNode : rootNodes) {
            TreeNode.buildTree(rootNode, childmap);
        }

        // 从根节点开始构建这个树
        Map<Long, List<RecruitingKpiByUserVO>> teamIdMap = voList.stream().collect(Collectors.groupingBy(vo -> {
            if (vo.getUserId() != null) {
                return vo.getUserId();
            } else {
                return vo.getTeamId();
            }
        }));

        // 5. 投影成 VO 树
        List<RecruitingKpiByUserVO> result = new ArrayList<>();
        for (TreeNode rootNode : rootNodes) {
            result.addAll(convertTreeNodeToVO(rootNode, teamIdMap));
        }

        return result;
    }

    private List<RecruitingKpiByUserVO> convertTreeNodeToVO(TreeNode node, Map<Long, List<RecruitingKpiByUserVO>> teamIdMap) {
        // 1. 找到当前 TreeNode 对应的 VO（考虑时间维度）
        List<RecruitingKpiByUserVO> candidates = teamIdMap.getOrDefault(node.getId(), Collections.emptyList());
        if (candidates.isEmpty()) {
            return Collections.emptyList();
        }
        if (candidates.size() == 1) {
            RecruitingKpiByUserVO result = candidates.get(0);
            // 3. 递归处理 children
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                List<RecruitingKpiByUserVO> childVos = node.getChildren().stream()
                        .map(child -> convertTreeNodeToVO(child, teamIdMap))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .toList();
                result.setChildren(childVos.stream().sorted(kpiComparator()).toList());
            }
            return List.of(result);
        } else  {
            String max = candidates.stream().map(RecruitingKpiCommonCountVO::getGroupByDate).max(Comparator.naturalOrder()).orElse("");
            List<RecruitingKpiByUserVO> result = new ArrayList<>();
            // 3. 递归处理 children

            List<RecruitingKpiByUserVO> childVos;
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                childVos = node.getChildren().stream()
                        .map(child -> convertTreeNodeToVO(child, teamIdMap))
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .sorted(kpiComparator())
                        .toList();
            } else {
                childVos = null;
            }
            candidates.stream().sorted(kpiComparator()).forEach(vo -> {
                if (vo.getGroupByDate().equals(max)) {
                    vo.setChildren(childVos);
                    result.add(vo);
                } else {
                    result.add(vo);
                }
            });

            return result;
        }

    }

    /**
     * 定义统一排序规则
     * 1. 团队 (userId == null) 在前，用户在后
     * 2. 相同团队id / 用户id 下，按 groupByDate 升序
     * 3. 不同用户 -> 按 userName 升序
     * 4. 不同团队 -> 按 teamId 升序
     */
    private Comparator<RecruitingKpiByUserVO> kpiComparator() {
        return Comparator
                // 团队在前，用户在后
                .comparing((RecruitingKpiByUserVO vo) -> vo.getUserId() == null ? 0 : 1)
                // 如果是团队 -> 按 teamId, 再按时间
                .thenComparing(vo -> vo.getUserId() == null ? vo.getTeamId() : null,
                        Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(RecruitingKpiByUserVO::getGroupByDate, Comparator.nullsLast(Comparator.naturalOrder()))
                // 如果是用户 -> 按 userName, 再按时间
                .thenComparing(vo -> vo.getUserId() != null ? vo.getUserName() : null,
                        Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(RecruitingKpiByUserVO::getGroupByDate, Comparator.nullsLast(Comparator.naturalOrder()));
    }


    /**
     * 构建时间线维度的树结构
     */
    private List<RecruitingKpiByUserVO> buildTimelineTree(List<RecruitingKpiByUserVO> voList) {
        Map<String, List<RecruitingKpiByUserVO>> dateGroups = voList.stream()
                .filter(vo -> vo.getGroupByDate() != null)
                .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getGroupByDate));

        List<RecruitingKpiByUserVO> result = new ArrayList<>();

//        for (Map.Entry<String, List<RecruitingKpiByUserVO>> dateEntry : dateGroups.entrySet()) {
//            String date = dateEntry.getKey();
//            List<RecruitingKpiByUserVO> dateVoList = dateEntry.getValue();
//
//            // 使用第一个VO作为时间根节点的模板
//            RecruitingKpiByUserVO dateRootNode = cloneVoForTree(dateVoList.get(0));
//            dateRootNode.setIsLeaf(false);
//            dateRootNode.setChildren(new ArrayList<>());
//
//            // 按团队分组并构建团队树
//            Map<Long, List<RecruitingKpiByUserVO>> teamGroups = dateVoList.stream()
//                    .filter(vo -> vo.getTeamId() != null)
//                    .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getTeamId));
//
//            // 构建团队节点
//            Map<Long, RecruitingKpiByUserVO> teamNodes = new HashMap<>();
//            for (Map.Entry<Long, List<RecruitingKpiByUserVO>> teamEntry : teamGroups.entrySet()) {
//                Long teamId = teamEntry.getKey();
//                RecruitingKpiByUserVO teamNode = teamEntry.getValue().stream()
//                        .filter(vo -> vo.getUserId() == null)
//                        .findFirst()
//                        .orElse(teamEntry.getValue().get(0));
//
//                RecruitingKpiByUserVO clonedTeamNode = cloneVoForTree(teamNode);
//                clonedTeamNode.setIsLeaf(false);
//                clonedTeamNode.setChildren(buildTeamChildrenSafe(teamId, date, dateVoList));
//                teamNodes.put(teamId, clonedTeamNode);
//            }
//
//            // 构建团队层级关系并找到根团队
//            Set<Long> childTeamIds = new HashSet<>();
//            for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
//                Long parentTeamId = teamNode.getTeamParentId();
//                if (parentTeamId != null && teamNodes.containsKey(parentTeamId)) {
//                    childTeamIds.add(teamNode.getTeamId());
//                    teamNodes.get(parentTeamId).getChildren().add(teamNode);
//                }
//            }
//
//            // 添加根团队到时间节点
//            for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
//                if (!childTeamIds.contains(teamNode.getTeamId())) {
//                    dateRootNode.getChildren().add(teamNode);
//                }
//            }
//
//            // 只有当时间节点有子数据时才添加
//            if (!dateRootNode.getChildren().isEmpty()) {
//                result.add(dateRootNode);
//            }
//        }
//
//        // 按时间排序
//        result.sort(Comparator.comparing(RecruitingKpiByUserVO::getGroupByDate, Comparator.nullsLast(String::compareTo)));
        return result;
    }


    @Getter
    @Setter
    private static class TreeNode {
        private Long id;
        private Long parentId;
        private String name;
        private String groupByDate;
        private List<TreeNode> children;

        public TreeNode(RecruitingKpiByUserVO vo) {
            if (vo.getUserId() != null) {
                this.id = vo.getUserId();
                this.name = vo.getUserName();
            } else {
                this.id = vo.getTeamId();
                this.name = vo.getTeamName();
            }
            this.parentId = vo.getTeamParentId();
            this.groupByDate = vo.getGroupByDate();
            this.children = new ArrayList<>();
        }

        public static Set<Long> findRoots(List<TreeNode> nodes) {
            // 收集所有 id
            Set<Long> allIds = nodes.stream()
                    .map(TreeNode::getId)
                    .collect(Collectors.toSet());

            // 根节点条件：parentId 不在 allIds 里，或者 parentId == -1
            return nodes.stream()
                    .filter(node -> node.getParentId() == -1 || !allIds.contains(node.getParentId()))
                    .map(TreeNode::getId)
                    .collect(Collectors.toSet());
        }

        public static void buildTree(TreeNode parent, Map<Long, List<TreeNode>> childrenMap) {
            List<TreeNode> children = childrenMap.get(parent.getId());
            if (children != null) {
                parent.setChildren(children);
                for (TreeNode child : children) {
                    buildTree(child, childrenMap);
                }
            }
        }


        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;

            TreeNode treeNode = (TreeNode) o;
            return id.equals(treeNode.id) && parentId.equals(treeNode.parentId) && groupByDate.equals(treeNode.groupByDate);
        }

        @Override
        public int hashCode() {
            int result = id.hashCode();
            result = 31 * result + parentId.hashCode();
            result = 31 * result + groupByDate.hashCode();
            return result;
        }
    }


}
