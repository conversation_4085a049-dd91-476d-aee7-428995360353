package com.altomni.apn.report.service.recruiting.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchKpiCompanyDto;
import com.altomni.apn.common.dto.recruiting.StageKpiReportDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.KpiReportByUserStageType;
import com.altomni.apn.common.enumeration.enums.KpiReportByUserStayedOver;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationAiTalentType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.enums.EnumMotivationService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.*;
import com.altomni.apn.common.vo.recruiting.v2.*;
import com.altomni.apn.report.config.StarRocksDelegatingExecutor;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.BDReportProgressNoteStatsVO;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.recruiting.RecruitingKpiBaseService;
import com.altomni.apn.report.service.user.UserService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;

@Slf4j
@Service("recruitingKpiBaseService")
public class RecruitingKpiBaseServiceImpl implements RecruitingKpiBaseService {

    @Resource
    protected UserService userService;

    @Resource
    protected CachePermission cachePermission;

    @Resource
    protected InitiationService initiationService;

    @Resource
    protected ReportRepository reportRepository;

    @Resource
    protected EnumMotivationService enumMotivationService;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Resource
    private HttpService httpService;

    protected static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("async-kpi-report-pool-").build();

    protected static final ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 10,
            Runtime.getRuntime().availableProcessors() * 20, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000), THREAD_FACTORY);

    protected final Executor starRocksExecutor = new StarRocksDelegatingExecutor(executorService);

    protected final Executor getExecutor() {
        return executorService;
    }

    protected final String ID_NAME_CONCAT = ";;;;;";
    protected final String DATA_JOIN_CONCAT = "||||||";


    protected void setJobSearchStatusDisplay(Map<Integer, EnumMotivation> enumMotivationMap, Integer jobSearchStatus, Object detailVo, SortType type) {
        if (ObjectUtil.isNotEmpty(jobSearchStatus)) {
            EnumMotivation enumMotivation = enumMotivationMap.get(jobSearchStatus);
            ReflectUtil.setFieldValue(detailVo, "jobSearchStatusDisplay", Objects.equals(type, SortType.CN)? enumMotivation.getCnDisplay(): enumMotivation.getEnDisplay());
        }
    }

    protected void setCreatedAndLastModified(Long createdBy, Instant createdDate, Long lastModifiedBy, Instant lastModifiedDate, String timezone, Object detailVo, Map<Long, UserBriefDTO> userMap) {
        if (ObjectUtil.isNotEmpty(createdBy)) {
            ReflectUtil.setFieldValue(detailVo, "createdBy", getUserNameByMap(createdBy, userMap));
        }
        if (ObjectUtil.isNotEmpty(createdDate) && StrUtil.isNotBlank(timezone)) {
            ReflectUtil.setFieldValue(detailVo, "createdDateFormat", formatDate(createdDate, timezone, "yyyy-MM-dd HH:mm:ss"));
        }
        if (ObjectUtil.isNotEmpty(lastModifiedBy)) {
            ReflectUtil.setFieldValue(detailVo, "lastModifiedBy", getUserNameByMap(lastModifiedBy, userMap));
        }
        if (ObjectUtil.isNotEmpty(lastModifiedDate) && StrUtil.isNotBlank(timezone)) {
            ReflectUtil.setFieldValue(detailVo, "lastModifiedDateFormat", formatDate(lastModifiedDate, timezone, "yyyy-MM-dd HH:mm:ss"));
        }
    }

    protected String getUserNameByMap(String userIds, Map<Long, UserBriefDTO> map) {
        if (StrUtil.isBlank(userIds)) {
            return null;
        }
        return Arrays.stream(userIds.split(","))
                .map(userId -> map.getOrDefault(Long.parseLong(userId), new UserBriefDTO()).getFullName())
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.joining(","));
    }

    protected String getUserNameByMap(Long userId, Map<Long, UserBriefDTO> map) {
        if (ObjectUtil.isEmpty(userId)) {
            return null;
        }
        return map.getOrDefault(userId, new UserBriefDTO()).getFullName();
    }

    protected String formatDate(Instant date, String timezone, String timeFormat) {
        return DateTimeFormatter.ofPattern(timeFormat)
                .withZone(ZoneId.of(timezone))
                .format(date);
    }
    public <T extends RecruitingKpiCommonCountVO> List<T> sortVoListByFields(List<T> voList, List<RecruitingKpiGroupByFieldType> groupByFields) {
        StopWatch sortWatch = new StopWatch("sort voList task");
        sortWatch.start();
        Comparator<RecruitingKpiCommonCountVO> comparator = (o1, o2) -> 0;
        for (RecruitingKpiGroupByFieldType field : groupByFields) {
            comparator = switch (field) {
                case USER -> comparator.thenComparing(vo -> vo.getUserName() + "-" + vo.getUserId(), Comparator.reverseOrder());
                case TEAM -> comparator.thenComparing(vo -> vo.getTeamName() + "-" + vo.getTeamId(), Comparator.reverseOrder());
                case COMPANY -> comparator.thenComparing(vo -> vo.getCompanyName() + "-" + vo.getCompanyId(), Comparator.reverseOrder());
                case JOB -> comparator.thenComparing(vo -> vo.getJobTitle() + "-" + vo.getJobId(), Comparator.reverseOrder());
                case DAY, WEEK, MONTH, QUARTER, YEAR ->
                        comparator.thenComparing(RecruitingKpiCommonCountVO::getGroupByDate, Comparator.reverseOrder());
                case TENANT -> comparator;
            };
        }
        voList = voList.stream()
                .sorted(comparator)
                .toList();
        sortWatch.stop();
        log.info("[apn @] search kpi sort time = {}ms", sortWatch.getTotalTimeMillis());
        return voList;
    }

    protected void setVoNumV2(RecruitingKpiByUserVO vo,
                              ConcurrentMap<String, RecruitingKpiApplicationCountVO> applicationMap,
                              Map<String, ReserveInterviewVO> reserveInterviewMap, ConcurrentMap<String, RecruitingKpiNoteCountVO> noteMap,
                              ConcurrentMap<String, KpiReportCreatedVO> createdDataMap,
                              ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap, String key, RecruitingKpiReportSearchDto searchDto) {
        // 获取默认值对象
        RecruitingKpiApplicationCountVO defaultApplication = new RecruitingKpiApplicationCountVO();
        RecruitingKpiNoteCountVO defaultNote = new RecruitingKpiNoteCountVO();
        KpiReportCreatedVO defaultCompany = new KpiReportCreatedVO();
        
        // 获取实际数据
        RecruitingKpiApplicationCountVO appData = applicationMap.getOrDefault(key, defaultApplication);
        ReserveInterviewVO reserveInterviewData = reserveInterviewMap.getOrDefault(key, new ReserveInterviewVO());
        RecruitingKpiNoteCountVO noteData = noteMap.getOrDefault(key, defaultNote);
        KpiReportCreatedVO createdData = createdDataMap.getOrDefault(key, defaultCompany);
        RecruitingKpiCommonCountVO jobData = jobMap.getOrDefault(key, new RecruitingKpiCommonCountVO());

        // 设置基础字段
        vo.setOpenings(jobData.getCountNum());
        vo.setTalentNum(createdData.getCreatedTalentCount());
        vo.setCreatedCompaniesNum(createdData.getCreatedCompanyCount());
        vo.setUpgradeToClient(createdData.getUpgradeCompanyCount());

        // 设置投递相关指标
        vo.setSubmitToJobNum(appData.getSubmitToJobCountNum());
        vo.setSubmitToJobCurrentNum(appData.getSubmitToJobCurrentCountNum());
        Integer submitToJobStayedOver = appData.getSubmitToJobStayedOver();
        Integer submitToJobCurrentStayedOver = appData.getSubmitToJobCurrentStayedOver();
        vo.setStayedOver24Hrs(submitToJobStayedOver != null && submitToJobStayedOver > 0 ? submitToJobStayedOver : null);
        vo.setCurrentStayedOver24Hrs(submitToJobCurrentStayedOver != null && submitToJobCurrentStayedOver > 0 ? submitToJobCurrentStayedOver : null);
        
        // 设置提交给客户相关指标
        vo.setSubmitToClientNum(appData.getSubmitToClientCountNum());
        vo.setSubmitToClientCurrentNum(appData.getSubmitToClientCurrentCountNum());
        Integer submitToClientStayedOver = appData.getSubmitToClientStayedOver();
        Integer submitToClientCurrentStayedOver = appData.getSubmitToClientCurrentStayedOver();
        vo.setStayedOver72Hrs(submitToClientStayedOver != null && submitToClientStayedOver > 0 ? submitToClientStayedOver : null);
        vo.setCurrentStayedOver72Hrs(submitToClientCurrentStayedOver != null && submitToClientCurrentStayedOver > 0 ? submitToClientCurrentStayedOver : null);
        
        // 设置面试相关指标
        vo.setFirstInterviewNum(appData.getInterview1());
        vo.setSecondInterviewNum(appData.getInterview2());
        vo.setFinalInterviewNum(appData.getInterviewFinal());
        vo.setInterviewNum(appData.getInterviewTotal());
        vo.setInterviewNumProcess(appData.getInterviewTotalProcess());
        vo.setCurrentFirstInterviewNum(appData.getCurrentInterview1());
        vo.setCurrentSecondInterviewNum(appData.getCurrentInterview2());
        vo.setCurrentFinalInterviewNum(appData.getCurrentInterviewFinal());
        vo.setCurrentInterviewNum(appData.getCurrentInterviewTotal());
        vo.setCurrentInterviewNumProcess(appData.getCurrentInterviewTotalProcess());
        vo.setTwoOrMoreInterviews(appData.getTwoOrMoreInterviews());
        vo.setCurrentTwoOrMoreInterviews(appData.getCurrentTwoOrMoreInterviews());
        vo.setUniqueInterviewTalentNum(appData.getUniqueInterviewTalents());

        // 设置预约面试相关指标
        // 当时间类型是 ADD 时，预约面试的数据就直接是 面试的数据相等
        vo.setInterviewAppointments(appData.getInterviewTotal());
        vo.setCurrentInterviewAppointments(appData.getCurrentInterviewTotal());
        if (!searchDto.getDateType().equals(RecruitingKpiDateType.ADD)) {
            vo.setInterviewAppointments(reserveInterviewData.getReserveInterviewTotal());
            vo.setCurrentInterviewAppointments(reserveInterviewData.getReserveCurrentInterviewTotal());
        }

        // 设置Offer相关指标
        vo.setOfferNum(appData.getOfferCountNum());
        vo.setOfferCurrentNum(appData.getOfferCurrentCountNum());
        vo.setOfferAcceptNum(appData.getOfferAcceptCountNum());
        vo.setOfferAcceptCurrentNum(appData.getOfferAcceptCurrentCountNum());
        
        // 设置入职相关指标
        vo.setOnboardNum(appData.getOnboardCountNum());
        vo.setOnboardCurrentNum(appData.getOnboardCurrentCountNum());
        
        // 设置淘汰相关指标
        vo.setEliminateNum(appData.getEliminateCountNum());
        vo.setEliminateCurrentNum(appData.getEliminateCurrentCountNum());
        
        // 设置笔记相关指标
        vo.setCallNoteNum(noteData.getCallNoteNum());
        vo.setPersonNoteNum(noteData.getPersonNoteNum());
        vo.setEmailNoteNum(noteData.getEmailNoteNum());
        vo.setVideoNoteNum(noteData.getVideoNoteNum());
        vo.setOtherNoteNum(noteData.getOtherNoteNum());
        vo.setIciNum(noteData.getIciNum());
        vo.setNoteCount(noteData.getNoteCount());
        vo.setUniqueTalentIds(noteData.getUniqueTalentIds());
        vo.setPipelineNoteNum(noteData.getApplicationNoteCountNum());
        vo.setApnProNoteNum(noteData.getTalentTrackingNoteCountNum());

        // 调用 AI 推荐相关设置
        setAIRecommendV2(vo, searchDto, appData, reserveInterviewData, key);
     }

     public void setAIRecommendV2(RecruitingKpiCommonVO vo,
                                   RecruitingKpiReportSearchDto searchDto,
                                   RecruitingKpiApplicationCountVO appData,
                                   ReserveInterviewVO reserveInterviewData,
                                   String key) {
         if (searchDto.getApplicationStatusType().equals(RecruitingKpiApplicationStatusType.ALL)) {
             // 设置 AI 推荐相关指标 (ALL 状态)
             vo.setSubmitToJobNumAIRecommend(appData.getSubmitToJobAiRecommendCountNum());
             vo.setSubmitToClientNumAIRecommend(appData.getSubmitToClientAiRecommendCountNum());
             vo.setFirstInterviewNumAIRecommend(appData.getInterview1AiRecommendNum());
             vo.setSecondInterviewNumAIRecommend(appData.getInterview2AiRecommendNum());
             vo.setFinalInterviewNumAIRecommend(appData.getInterviewFinalAiRecommendNum());
             vo.setInterviewNumAIRecommend(appData.getInterviewTotalAiRecommendNum());
             vo.setInterviewNumProcessAIRecommend(appData.getInterviewTotalProcessAiRecommendNum());
             vo.setTwoOrMoreInterviewsAIRecommend(appData.getTwoOrMoreInterviewsAiRecommendNum());
             vo.setOfferNumAIRecommend(appData.getOfferAiRecommendCountNum());
             vo.setOfferAcceptNumAIRecommend(appData.getOfferAcceptAiRecommendCountNum());
             vo.setOnboardNumAIRecommend(appData.getOnboardAiRecommendCountNum());
             vo.setEliminateNumAIRecommend(appData.getEliminateAiRecommendCountNum());
             vo.setInterviewAppointmentsAIRecommend(appData.getInterviewTotalAiRecommendNum());

             // 设置精准推荐相关指标 (ALL 状态)
             vo.setSubmitToJobNumPrecisionAIRecommend(appData.getSubmitToJobPrecisionAiRecommendNum());
             vo.setSubmitToClientNumPrecisionAIRecommend(appData.getSubmitToClientPrecisionAiRecommendNum());
             vo.setFirstInterviewNumPrecisionAIRecommend(appData.getInterview1PrecisionAiRecommendNum());
             vo.setSecondInterviewNumPrecisionAIRecommend(appData.getInterview2PrecisionAiRecommendNum());
             vo.setFinalInterviewNumPrecisionAIRecommend(appData.getInterviewFinalPrecisionAiRecommendNum());
             vo.setInterviewNumPrecisionAIRecommend(appData.getInterviewTotalPrecisionAiRecommendNum());
             vo.setInterviewNumProcessPrecisionAIRecommend(appData.getInterviewNumProcessPrecisionAIRecommend());
             vo.setTwoOrMoreInterviewsPrecisionAIRecommend(appData.getTwoOrMoreInterviewsPrecisionAiRecommendNum());
             vo.setOfferNumPrecisionAIRecommend(appData.getOfferPrecisionAiRecommendNum());
             vo.setOfferAcceptNumPrecisionAIRecommend(appData.getOfferAcceptPrecisionAiRecommendNum());
             vo.setOnboardNumPrecisionAIRecommend(appData.getOnboardPrecisionAiRecommendNum());
             vo.setEliminateNumPrecisionAIRecommend(appData.getEliminatePrecisionAiRecommendNum());
             vo.setInterviewAppointmentsPrecisionAIRecommend(appData.getInterviewTotalPrecisionAiRecommendNum());

             // 当时间类型是 ADD 时，预约面试的数据就直接是 面试的数据相等
             if (!searchDto.getDateType().equals(RecruitingKpiDateType.ADD)) {
                 vo.setInterviewAppointmentsAIRecommend(reserveInterviewData.getReserveInterviewAiRecommendCountNum());
                 vo.setInterviewAppointmentsPrecisionAIRecommend(reserveInterviewData.getReserveInterviewPrecisionAiRecommendNum());
             }

             if (null != searchDto.getAiTalentType()) {

                 if ((null != vo.getOnboardNum() && vo.getOnboardNum() != 0) && (null != vo.getSubmitToJobNum() && vo.getSubmitToJobNum() != 0)) {
                     vo.setOverallConversionRate(new BigDecimal(vo.getOnboardNum()).divide(new BigDecimal(vo.getSubmitToJobNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                 }

                 BigDecimal overallIntegerView = new BigDecimal(vo.getInterviewNumProcess() == null ? 0 : vo.getInterviewNumProcess());
                 if (null != overallIntegerView && (null != vo.getSubmitToJobNum() && vo.getSubmitToJobNum() != 0)) {
                     vo.setOverallInterviewConversionRate(overallIntegerView.divide(new BigDecimal(vo.getSubmitToJobNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                 }

                 if (searchDto.getAiTalentType().equals(RecruitingKpiApplicationAiTalentType.TALENT_COUNT)) {
                     BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getInterviewNumProcessAIRecommend() == null ? 0 : vo.getInterviewNumProcessAIRecommend());
                     if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                         vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }

                     BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumAIRecommend() == null ? 0L : vo.getOnboardNumAIRecommend());
                     if ((null != vo.getOnboardNumAIRecommend() && vo.getOnboardNumAIRecommend() != 0) && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                         vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }
                 } else {
                     BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getInterviewNumProcessPrecisionAIRecommend() == null ? 0 : vo.getInterviewNumProcessPrecisionAIRecommend());
                     if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                         vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }

                     BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumPrecisionAIRecommend() == null ? 0L : vo.getOnboardNumPrecisionAIRecommend());
                     if ((null != vo.getOnboardNumPrecisionAIRecommend() && vo.getOnboardNumPrecisionAIRecommend() != 0) && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                         vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }
                 }
             }

         } else {
             // 设置 AI 推荐相关指标 (CURRENT 状态)
             vo.setSubmitToJobNumAIRecommend(appData.getSubmitToJobCurrentAiRecommendNum());
             vo.setSubmitToClientNumAIRecommend(appData.getSubmitToClientCurrentAiRecommendNum());
             vo.setFirstInterviewNumAIRecommend(appData.getCurrentInterview1AiRecommendNum());
             vo.setSecondInterviewNumAIRecommend(appData.getCurrentInterview2AiRecommendNum());
             vo.setFinalInterviewNumAIRecommend(appData.getCurrentInterviewFinalAiRecommendNum());
             vo.setInterviewNumAIRecommend(appData.getCurrentInterviewTotalAiRecommendNum());
             vo.setTwoOrMoreInterviewsAIRecommend(appData.getCurrentTwoOrMoreInterviewsAiRecommendNum());
             vo.setOfferNumAIRecommend(appData.getOfferCurrentAiRecommendNum());
             vo.setOfferAcceptNumAIRecommend(appData.getOfferAcceptCurrentAiRecommendNum());
             vo.setOnboardNumAIRecommend(appData.getOnboardCurrentAiRecommendNum());
             vo.setEliminateNumAIRecommend(appData.getEliminateCurrentAiRecommendNum());
             vo.setInterviewAppointmentsAIRecommend(appData.getCurrentInterviewTotalAiRecommendNum());
             vo.setCurrentInterviewNumProcessAIRecommend(appData.getCurrentInterviewTotalProcessAiRecommendNum());

             // 设置精准推荐相关指标 (CURRENT 状态)
             vo.setSubmitToJobNumPrecisionAIRecommend(appData.getSubmitToJobCurrentPrecisionAiRecommendNum());
             vo.setSubmitToClientNumPrecisionAIRecommend(appData.getSubmitToClientCurrentPrecisionAiRecommendNum());
             vo.setFirstInterviewNumPrecisionAIRecommend(appData.getCurrentInterview1PrecisionAiRecommendNum());
             vo.setSecondInterviewNumPrecisionAIRecommend(appData.getCurrentInterview2PrecisionAiRecommendNum());
             vo.setFinalInterviewNumPrecisionAIRecommend(appData.getCurrentInterviewFinalPrecisionAiRecommendNum());
             vo.setInterviewNumPrecisionAIRecommend(appData.getCurrentInterviewTotalPrecisionAiRecommendNum());
             vo.setTwoOrMoreInterviewsPrecisionAIRecommend(appData.getCurrentTwoOrMoreInterviewsPrecisionAiRecommendNum());
             vo.setOfferNumPrecisionAIRecommend(appData.getOfferCurrentPrecisionAiRecommendNum());
             vo.setOfferAcceptNumPrecisionAIRecommend(appData.getOfferAcceptCurrentPrecisionAiRecommendNum());
             vo.setOnboardNumPrecisionAIRecommend(appData.getOnboardCurrentPrecisionAiRecommendNum());
             vo.setEliminateNumPrecisionAIRecommend(appData.getEliminateCurrentPrecisionAiRecommendNum());
             vo.setInterviewAppointmentsPrecisionAIRecommend(appData.getCurrentInterviewTotalPrecisionAiRecommendNum());
             vo.setCurrentInterviewNumProcessPrecisionAIRecommend(appData.getCurrentInterviewNumProcessPrecisionAIRecommend());

             // 当时间类型是 ADD 时，预约面试的数据就直接是 面试的数据相等
             if (!searchDto.getDateType().equals(RecruitingKpiDateType.ADD)) {
                 vo.setInterviewAppointmentsAIRecommend(reserveInterviewData.getReserveInterviewAiRecommendCountNum());
                 vo.setCurrentInterviewNumProcessPrecisionAIRecommend(reserveInterviewData.getReserveInterviewCurrentPrecisionAiRecommendNum());
             }

             if (null != searchDto.getAiTalentType()) {

                 if ((null != vo.getOnboardNum() && vo.getOnboardNum() != 0) && (null != vo.getSubmitToJobCurrentNum() && vo.getSubmitToJobCurrentNum() != 0)) {
                     vo.setOverallConversionRate(new BigDecimal(vo.getOnboardNum()).divide(new BigDecimal(vo.getSubmitToJobCurrentNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                 }

                 BigDecimal overallIntegerView = new BigDecimal(vo.getCurrentInterviewNumProcess() == null ? 0 : vo.getCurrentInterviewNumProcess());
                 if (null != overallIntegerView && (null != vo.getSubmitToJobCurrentNum() && vo.getSubmitToJobCurrentNum() != 0)) {
                     vo.setOverallInterviewConversionRate(overallIntegerView.divide(new BigDecimal(vo.getSubmitToJobCurrentNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                 }

                 if (searchDto.getAiTalentType().equals(RecruitingKpiApplicationAiTalentType.TALENT_COUNT)) {
                     BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getCurrentInterviewNumProcessAIRecommend() == null ? 0 : vo.getCurrentInterviewNumProcessAIRecommend());
                     if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                         vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }

                     BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumAIRecommend() == null ? 0L : vo.getOnboardNumAIRecommend());
                     if ((null != vo.getOnboardNumAIRecommend() && vo.getOnboardNumAIRecommend() != 0) && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                         vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }
                 } else {
                     BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getCurrentInterviewNumProcessPrecisionAIRecommend()== null ? 0 : vo.getCurrentInterviewNumProcessPrecisionAIRecommend());
                     if (null != overallIntegerViewPrecisionAIRecommend  && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                         vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }

                     BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumPrecisionAIRecommend() == null ? 0L : vo.getOnboardNumPrecisionAIRecommend());
                     if ((null != vo.getOnboardNumPrecisionAIRecommend() && vo.getOnboardNumPrecisionAIRecommend() != 0) && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                         vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                     }
                 }
             }
         }
     }

    /**
     *
     * @param vo
     * @param jobMap
     * @param talentMap
     * @param submitToJobMap
     * @param submitToClientMap
     * @param interviewMap
     * @param reserveInterviewMap
     * @param offerMap
     * @param offerAcceptMap
     * @param onboardMap
     * @param eliminateMap
     * @param key
     * @param searchDto
     */
    protected void setVoNum(RecruitingKpiCommonVO vo,
                          ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap,
                          ConcurrentMap<String, RecruitingKpiCommonCountVO> talentMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToClientMap,
                          ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap,
                          ConcurrentMap<String, RecruitingKpiInterviewCountVO> reserveInterviewMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerAcceptMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap,
                          ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> eliminateMap,
                          String key,RecruitingKpiReportSearchDto searchDto) {
        if (CollUtil.isNotEmpty(jobMap)) {
            vo.setOpenings(jobMap.getOrDefault(key, new RecruitingKpiCommonCountVO()).getCountNum());
            vo.setCompanyNum(jobMap.getOrDefault(key, new RecruitingKpiCommonCountVO()).getCompanyNum());
        }
        vo.setTalentNum(talentMap.getOrDefault(key, new RecruitingKpiCommonCountVO()).getCountNum());
        vo.setSubmitToJobNum(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setSubmitToJobCurrentNum(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setStayedOver24Hrs(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getStayedOver());
        vo.setCurrentStayedOver24Hrs(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentStayedOver());
        vo.setSubmitToClientNum(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setStayedOver72Hrs(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getStayedOver());
        vo.setCurrentStayedOver72Hrs(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentStayedOver());
        vo.setSubmitToClientCurrentNum(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setFirstInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview1());
        vo.setSecondInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview2());
        vo.setFinalInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewFinal());
        vo.setInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotal());
        vo.setInterviewNumProcess(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalProcess());
        vo.setCurrentFirstInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview1());
        vo.setCurrentSecondInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview2());
        vo.setCurrentFinalInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewFinal());
        vo.setCurrentInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotal());
        vo.setCurrentInterviewNumProcess(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalProcess());
        vo.setTwoOrMoreInterviews(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getTwoOrMoreInterviews());
        vo.setCurrentTwoOrMoreInterviews(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentTwoOrMoreInterviews());
        vo.setUniqueInterviewTalentNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getUniqueInterviewTalents());
        vo.setInterviewAppointments(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotal());
        vo.setCurrentInterviewAppointments(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotal());
        if (reserveInterviewMap != null) {
            vo.setInterviewAppointments(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotal());
            vo.setCurrentInterviewAppointments(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotal());
        }
        vo.setOfferNum(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setOfferCurrentNum(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setOfferAcceptNum(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setOfferAcceptCurrentNum(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setOnboardNum(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setOnboardCurrentNum(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setEliminateNum(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setEliminateCurrentNum(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        setAIRecommend(vo,searchDto,submitToJobMap,submitToClientMap,interviewMap,reserveInterviewMap,offerMap,offerAcceptMap,onboardMap,eliminateMap,key);
    }

    protected void setVoNumForE5(RecruitingKpiCommonVO vo,
                                 ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap,
                                 ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap,
                                 ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap,
                                 String key,
                                 RecruitingKpiReportSearchDto searchDto) {

        vo.setSubmitToJobNum(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setSubmitToJobCurrentNum(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
        vo.setStayedOver24Hrs(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getStayedOver());
        vo.setCurrentStayedOver24Hrs(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentStayedOver());
        vo.setFirstInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview1());
        vo.setSecondInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview2());
        vo.setFinalInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewFinal());
        vo.setInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotal());
        vo.setInterviewNumProcess(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalProcess());
        vo.setCurrentFirstInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview1());
        vo.setCurrentSecondInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview2());
        vo.setCurrentFinalInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewFinal());
        vo.setCurrentInterviewNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotal());
        vo.setCurrentInterviewNumProcess(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalProcess());
        vo.setTwoOrMoreInterviews(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getTwoOrMoreInterviews());
        vo.setCurrentTwoOrMoreInterviews(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentTwoOrMoreInterviews());
        vo.setUniqueInterviewTalentNum(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getUniqueInterviewTalents());
        vo.setInterviewAppointments(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotal());
        vo.setCurrentInterviewAppointments(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotal());
        vo.setOnboardNum(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCountNum());
        vo.setOnboardCurrentNum(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentCountNum());
    }

    private void setAIRecommend(RecruitingKpiCommonVO vo,
                                RecruitingKpiReportSearchDto searchDto,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToJobMap,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> submitToClientMap,
                                ConcurrentMap<String, RecruitingKpiInterviewCountVO> interviewMap,
                                ConcurrentMap<String, RecruitingKpiInterviewCountVO> reserveInterviewMap,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerMap,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> offerAcceptMap,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> onboardMap,
                                ConcurrentMap<String, RecruitingKpiApplicationCommonCountVO> eliminateMap,
                                String key){
        if (searchDto.getApplicationStatusType().equals(RecruitingKpiApplicationStatusType.ALL)) {
            vo.setSubmitToJobNumAIRecommend(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setSubmitToClientNumAIRecommend(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setFirstInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview1AiRecommendNum());
            vo.setSecondInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview2AiRecommendNum());
            vo.setFinalInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewFinalAiRecommendNum());
            vo.setInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalAiRecommendNum());
            vo.setInterviewNumProcessAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalProcessAiRecommendNum());
            vo.setTwoOrMoreInterviewsAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getTwoOrMoreInterviewsAiRecommendNum());
            vo.setOfferNumAIRecommend(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setOfferAcceptNumAIRecommend(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setOnboardNumAIRecommend(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setEliminateNumAIRecommend(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getAiRecommendNum());
            vo.setInterviewAppointmentsAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalAiRecommendNum());

            //通过加入职位的ai推荐 精准推荐
            vo.setSubmitToJobNumPrecisionAIRecommend(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setSubmitToClientNumPrecisionAIRecommend(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setFirstInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview1PrecisionAiRecommendNum());
            vo.setSecondInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterview2PrecisionAiRecommendNum());
            vo.setFinalInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewFinalPrecisionAiRecommendNum());
            vo.setInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalPrecisionAiRecommendNum());
            vo.setInterviewNumProcessPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewNumProcessPrecisionAIRecommend());
            vo.setTwoOrMoreInterviewsPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getTwoOrMoreInterviewsPrecisionAiRecommendNum());
            vo.setOfferNumPrecisionAIRecommend(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setOfferAcceptNumPrecisionAIRecommend(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setOnboardNumPrecisionAIRecommend(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setEliminateNumPrecisionAIRecommend(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getPrecisionAiRecommendNum());
            vo.setInterviewAppointmentsPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getInterviewTotalPrecisionAiRecommendNum());
            if (reserveInterviewMap != null) {
                vo.setInterviewAppointmentsAIRecommend(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getAiRecommendNum());
                vo.setInterviewAppointmentsPrecisionAIRecommend(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getPrecisionAiRecommendNum());
            }

            if (null != searchDto.getAiTalentType()) {

                if ((null != vo.getOnboardNum() && vo.getOnboardNum() != 0) && (null != vo.getSubmitToJobNum() && vo.getSubmitToJobNum() != 0)) {
                    vo.setOverallConversionRate(new BigDecimal(vo.getOnboardNum()).divide(new BigDecimal(vo.getSubmitToJobNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                }

                BigDecimal overallIntegerView = new BigDecimal(vo.getInterviewNumProcess() == null ? 0 : vo.getInterviewNumProcess());
                if (null != overallIntegerView && (null != vo.getSubmitToJobNum() && vo.getSubmitToJobNum() != 0)) {
                    vo.setOverallInterviewConversionRate(overallIntegerView.divide(new BigDecimal(vo.getSubmitToJobNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                }

                if (searchDto.getAiTalentType().equals(RecruitingKpiApplicationAiTalentType.TALENT_COUNT)) {
                    BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getInterviewNumProcessAIRecommend() == null ? 0 : vo.getInterviewNumProcessAIRecommend());
                    if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                        vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }

                    BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumAIRecommend() == null ? 0L : vo.getOnboardNumAIRecommend());
                    if ((null != vo.getOnboardNumAIRecommend() && vo.getOnboardNumAIRecommend() != 0) && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                        vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }
                } else {
                    BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getInterviewNumProcessPrecisionAIRecommend() == null ? 0 : vo.getInterviewNumProcessPrecisionAIRecommend());
                    if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                        vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }

                    BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumPrecisionAIRecommend() == null ? 0L : vo.getOnboardNumPrecisionAIRecommend());
                    if ((null != vo.getOnboardNumPrecisionAIRecommend() && vo.getOnboardNumPrecisionAIRecommend() != 0) && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                        vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }
                }
            }

        } else {
            vo.setSubmitToJobNumAIRecommend(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setSubmitToClientNumAIRecommend(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setFirstInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview1AiRecommendNum());
            vo.setSecondInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview2AiRecommendNum());
            vo.setFinalInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewFinalAiRecommendNum());
            vo.setInterviewNumAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalAiRecommendNum());
            vo.setTwoOrMoreInterviewsAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentTwoOrMoreInterviewsAiRecommendNum());
            vo.setOfferNumAIRecommend(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setOfferAcceptNumAIRecommend(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setOnboardNumAIRecommend(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setEliminateNumAIRecommend(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentAiRecommendNum());
            vo.setInterviewAppointmentsAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalAiRecommendNum());
            vo.setCurrentInterviewNumProcessAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalProcessAiRecommendNum());

            //通过加入职位的ai推荐 精准推荐 Precision
            vo.setSubmitToJobNumPrecisionAIRecommend(submitToJobMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setSubmitToClientNumPrecisionAIRecommend(submitToClientMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setFirstInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview1PrecisionAiRecommendNum());
            vo.setSecondInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterview2PrecisionAiRecommendNum());
            vo.setFinalInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewFinalPrecisionAiRecommendNum());
            vo.setInterviewNumPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalPrecisionAiRecommendNum());
            vo.setTwoOrMoreInterviewsPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentTwoOrMoreInterviewsPrecisionAiRecommendNum());
            vo.setOfferNumPrecisionAIRecommend(offerMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setOfferAcceptNumPrecisionAIRecommend(offerAcceptMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setOnboardNumPrecisionAIRecommend(onboardMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setEliminateNumPrecisionAIRecommend(eliminateMap.getOrDefault(key, new RecruitingKpiApplicationCommonCountVO()).getCurrentPrecisionAiRecommendNum());
            vo.setInterviewAppointmentsPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewTotalPrecisionAiRecommendNum());
            vo.setCurrentInterviewNumProcessPrecisionAIRecommend(interviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentInterviewNumProcessPrecisionAIRecommend());
            if (reserveInterviewMap != null) {
                vo.setInterviewAppointmentsAIRecommend(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentAiRecommendNum());
                vo.setInterviewAppointmentsPrecisionAIRecommend(reserveInterviewMap.getOrDefault(key, new RecruitingKpiInterviewCountVO()).getCurrentPrecisionAiRecommendNum());
            }

            if (null != searchDto.getAiTalentType()) {

                if ((null != vo.getOnboardNum() && vo.getOnboardNum() != 0) && (null != vo.getSubmitToJobCurrentNum() && vo.getSubmitToJobCurrentNum() != 0)) {
                    vo.setOverallConversionRate(new BigDecimal(vo.getOnboardNum()).divide(new BigDecimal(vo.getSubmitToJobCurrentNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                }

                BigDecimal overallIntegerView = new BigDecimal(vo.getCurrentInterviewNumProcess() == null ? 0 : vo.getCurrentInterviewNumProcess());
                if (null != overallIntegerView && (null != vo.getSubmitToJobCurrentNum() && vo.getSubmitToJobCurrentNum() != 0)) {
                    vo.setOverallInterviewConversionRate(overallIntegerView.divide(new BigDecimal(vo.getSubmitToJobCurrentNum()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                }

                if (searchDto.getAiTalentType().equals(RecruitingKpiApplicationAiTalentType.TALENT_COUNT)) {
                    BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getCurrentInterviewNumProcessAIRecommend() == null ? 0 : vo.getCurrentInterviewNumProcessAIRecommend());
                    if (null != overallIntegerViewPrecisionAIRecommend && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                        vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()), 5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }

                    BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumAIRecommend() == null ? 0L : vo.getOnboardNumAIRecommend());
                    if ((null != vo.getOnboardNumAIRecommend() && vo.getOnboardNumAIRecommend() != 0) && (null != vo.getSubmitToJobNumAIRecommend() && vo.getSubmitToJobNumAIRecommend() != 0)) {
                        vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }
                } else {
                    BigDecimal overallIntegerViewPrecisionAIRecommend = new BigDecimal(vo.getCurrentInterviewNumProcessPrecisionAIRecommend()== null ? 0 : vo.getCurrentInterviewNumProcessPrecisionAIRecommend());
                    if (null != overallIntegerViewPrecisionAIRecommend  && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                        vo.setAiInterviewConversionRate(overallIntegerViewPrecisionAIRecommend.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }

                    BigDecimal bigDecimal = new BigDecimal(vo.getOnboardNumPrecisionAIRecommend() == null ? 0L : vo.getOnboardNumPrecisionAIRecommend());
                    if ((null != vo.getOnboardNumPrecisionAIRecommend() && vo.getOnboardNumPrecisionAIRecommend() != 0) && (null != vo.getSubmitToJobNumPrecisionAIRecommend() && vo.getSubmitToJobNumPrecisionAIRecommend() != 0)) {
                        vo.setAiConversionRate(bigDecimal.divide(new BigDecimal(vo.getSubmitToJobNumPrecisionAIRecommend()),5, BigDecimal.ROUND_HALF_UP).setScale(4, BigDecimal.ROUND_UP).toString());
                    }
                }
            }

        }
    }

    protected void setGroupByField(RecruitingKpiCommonCountVO vo, String key, List<RecruitingKpiGroupByFieldType> groupByFieldTypeList) {
        String[] values = key.split("\\|\\|\\|\\|\\|\\|");
        try {
            for (int i = 0; i < groupByFieldTypeList.size(); i++) {
                switch (groupByFieldTypeList.get(i)) {
                    case COMPANY -> {
                        vo.setCompanyId(Long.parseLong(values[i].split(ID_NAME_CONCAT)[1]));
                        vo.setCompanyName(values[i].split(ID_NAME_CONCAT)[0]);
                    }
                    case JOB -> {
                        vo.setJobId(Long.parseLong(values[i].split(ID_NAME_CONCAT)[1]));
                        vo.setJobTitle(values[i].split(ID_NAME_CONCAT)[0]);
                    }
                    case USER -> {
                        vo.setUserId(Long.parseLong(values[i].split(ID_NAME_CONCAT)[1]));
                        vo.setUserName(values[i].split(ID_NAME_CONCAT)[0]);
                    }
                    case TEAM -> {
                        vo.setTeamId(Long.parseLong(values[i].split(ID_NAME_CONCAT)[1]));
                        vo.setTeamName(values[i].split(ID_NAME_CONCAT)[0]);
                        String[] parts = values[i].split(ID_NAME_CONCAT);
                        if (parts.length > 2) {
                            Optional.ofNullable(parts[2]).ifPresent(parentId -> vo.setTeamParentId(Long.parseLong(parentId)));
                        }
                        if (parts.length > 3) {
                            Optional.ofNullable(parts[3]).ifPresent(teamLevel -> vo.setTeamLevel(Integer.parseInt(teamLevel)));
                        }
                        if (parts.length > 4) {
                            Optional.ofNullable(parts[4]).ifPresent(isLeaf -> vo.setIsLeaf(Boolean.parseBoolean(isLeaf)));
                        }
                    }
                    case TENANT -> vo.setTenantId(Long.parseLong(values[i]));
                    case DAY, WEEK, MONTH, QUARTER, YEAR -> vo.setGroupByDate(values[i]);
                }
            }
        } catch (Exception e) {
            log.error(" get id from key is error , msg = {}", ExceptionUtil.getAllExceptionMsg(e));
            throw new RuntimeException(" set group field is error");
        }
    }

    protected Function<RecruitingKpiCommonCountVO, String> getMapKey(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return kpiCommonCountVO -> groupByFieldList.stream()
                .map(groupByFieldType -> switch (groupByFieldType) {
                    case COMPANY -> kpiCommonCountVO.getCompanyName() + ID_NAME_CONCAT + kpiCommonCountVO.getCompanyId();
                    case TENANT -> kpiCommonCountVO.getTenantId().toString();
                    case JOB -> kpiCommonCountVO.getJobTitle() + ID_NAME_CONCAT + kpiCommonCountVO.getJobId();
                    case USER -> kpiCommonCountVO.getUserName() + ID_NAME_CONCAT + kpiCommonCountVO.getUserId();
                    case TEAM -> kpiCommonCountVO.getTeamName() + ID_NAME_CONCAT + kpiCommonCountVO.getTeamId() + ID_NAME_CONCAT + kpiCommonCountVO.getTeamParentId() + ID_NAME_CONCAT + kpiCommonCountVO.getTeamLevel() + ID_NAME_CONCAT + kpiCommonCountVO.getIsLeaf();
                    case DAY, WEEK, MONTH, QUARTER, YEAR -> kpiCommonCountVO.getGroupByDate();
                }).collect(Collectors.joining(DATA_JOIN_CONCAT));
    }

    protected Function<RecruitingKpiCommonCountVO, String> getCompanyMapKey(List<RecruitingKpiGroupByFieldType> groupByFieldList) {
        return kpiCommonCountVO -> groupByFieldList.stream()
                .map(groupByFieldType -> switch (groupByFieldType) {
                    case COMPANY -> kpiCommonCountVO.getCompanyName() + ID_NAME_CONCAT + kpiCommonCountVO.getCompanyId();
                    case TENANT -> kpiCommonCountVO.getTenantId().toString();
                    case JOB -> kpiCommonCountVO.getJobTitle() + ID_NAME_CONCAT + kpiCommonCountVO.getJobId();
                    case USER -> kpiCommonCountVO.getUserName() + ID_NAME_CONCAT + kpiCommonCountVO.getUserId();
                    case TEAM -> kpiCommonCountVO.getTeamName() + ID_NAME_CONCAT + kpiCommonCountVO.getTeamId();
                    case DAY, WEEK, MONTH, QUARTER, YEAR -> kpiCommonCountVO.getGroupByDate();
                }).collect(Collectors.joining(DATA_JOIN_CONCAT));
    }

    protected Long getTenantId(Long searchTenantId) {
        return Optional.ofNullable(searchTenantId).orElseGet(SecurityUtils::getTenantId);
    }

    protected Long getUserId(Long searchUserId) {
        return Optional.ofNullable(searchUserId).orElseGet(SecurityUtils::getUserId);
    }

    protected TeamDataPermissionRespDTO getPermissionDTOAndSetCommonParam(RecruitingKpiReportSearchDto searchDto) {
        Long userId = getUserId(searchDto.getSearchUserId());
        Long tenantId = getTenantId(searchDto.getSearchTenantId());
        searchDto.setSearchUserId(userId);
        searchDto.setSearchTenantId(tenantId);
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(tenantId, userId).getBody();
        log.info("kpi report permission = {}", JSONUtil.toJsonStr(teamDataPermission));
        // 报表中汇总计算私有职位数据
        searchDto.setPrivateJobIds(reportRepository.getJobIdsForPrivateJob(searchDto.getSearchUserId(), teamDataPermission.getTeamIdForPrivateJob()));
        if (BooleanUtil.isTrue(searchDto.isXxlJobFlag())) {
            //如果是xxl-job 使用最大权限
            teamDataPermission = new TeamDataPermissionRespDTO();
            teamDataPermission.setAll(true);
            teamDataPermission.setSelf(false);
            teamDataPermission.setNestedTeamIds(new HashSet<>());
        }
        searchDto.setPermissionRespDTO(teamDataPermission);
        return teamDataPermission;
    }

    protected List<StageKpiReportDto> addStageFilter(RecruitingKpiReportSearchDto searchDto){
        List<StageKpiReportDto> stageKpiReportDtos = new ArrayList<>();
        if (null != searchDto.getJobCountPositions()) {
            StageKpiReportDto bean = new StageKpiReportDto();
            bean.setStageName(KpiReportByUserStageType.JOB_COUNT);
            bean.setMinValue(searchDto.getJobCountPositions().getMinValue());
            bean.setMaxValue(searchDto.getJobCountPositions().getMaxValue());
            stageKpiReportDtos.add(bean);
        }
        if (null != searchDto.getTalentStageList() && !searchDto.getTalentStageList().isEmpty()) {
            stageKpiReportDtos.addAll(searchDto.getTalentStageList());
        }
        if (null != searchDto.getNotesStageList() && !searchDto.getNotesStageList().isEmpty()) {
            stageKpiReportDtos.addAll(searchDto.getNotesStageList());
        }
        return stageKpiReportDtos;
    }

    private ToLongFunction<RecruitingKpiByUserVO> getFieldGetter(KpiReportByUserStageType stageName, RecruitingKpiApplicationStatusType applicationStatusType) {
        return switch (stageName) {
            case TALENT_NUM -> vo -> safeLong(vo.getTalentNum());
            case SUBMIT_TO_JOB_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getSubmitToJobNum(), vo.getSubmitToJobCurrentNum()));
            case SUBMIT_TO_CLIENT_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getSubmitToClientNum(), vo.getSubmitToClientCurrentNum()));
            case FIRST_INTERVIEW_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getFirstInterviewNum(), vo.getCurrentFirstInterviewNum()));
            case SECOND_INTERVIEW_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getSecondInterviewNum(), vo.getCurrentSecondInterviewNum()));
            case FINAL_INTERVIEW_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getFinalInterviewNum(), vo.getCurrentFinalInterviewNum()));
            case INTERVIEW_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getInterviewNum(), vo.getCurrentInterviewNum()));
            case OFFER_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getOfferNum(), vo.getOfferCurrentNum()));
            case OFFER_ACCEPT_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getOfferAcceptNum(), vo.getOfferAcceptCurrentNum()));
            case ONBOARD_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getOnboardNum(), vo.getOnboardCurrentNum()));
            case ELIMINATE_NUM -> vo -> safeLong(getFieldByStatus(applicationStatusType, vo.getEliminateNum(), vo.getEliminateCurrentNum()));
            case CALL_NOTE_NUM -> vo -> safeLong(vo.getCallNoteNum());
            case EMAIL_NOTE_NUM -> vo -> safeLong(vo.getEmailNoteNum());
            case PERSON_NOTE_NUM -> vo -> safeLong(vo.getPersonNoteNum());
            case VIDEO_NOTE_NUM -> vo -> safeLong(vo.getVideoNoteNum());
            case OTHER_NOTE_NUM -> vo -> safeLong(vo.getOtherNoteNum());
            case PIPELINE_NOTE_NUM -> vo -> safeLong(vo.getPipelineNoteNum());
            case APN_PRO_NOTE_NUM -> vo -> safeLong(vo.getApnProNoteNum());
            case ICI_NUM -> vo -> safeLong(vo.getIciNum());
            case JOB_COUNT -> vo -> safeLong(vo.getOpenings());
            default -> null;
        };
    }

    /**
     * 根据应用状态类型选择对应的字段值
     * @param applicationStatusType 应用状态类型
     * @param allValue ALL状态对应的字段值
     * @param currentValue CURRENT状态对应的字段值
     * @return 选择的字段值
     */
    private Long getFieldByStatus(RecruitingKpiApplicationStatusType applicationStatusType, Long allValue, Long currentValue) {
        return applicationStatusType.equals(RecruitingKpiApplicationStatusType.ALL) ? allValue : currentValue;
    }

    /**
     * 过滤高级搜索条件，针对查询结果过滤
     * @param vo
     * @param stageKpiReportDto
     * @param applicationStatusType
     * @return
     */
    protected boolean checkSearchFilter(RecruitingKpiByUserVO vo, List<StageKpiReportDto> stageKpiReportDto,
                                      RecruitingKpiApplicationStatusType applicationStatusType,
                                      List<KpiReportByUserStayedOver> stayedOverList){
        if (null != stageKpiReportDto && !stageKpiReportDto.isEmpty()) {
            for (StageKpiReportDto x : stageKpiReportDto) {
                ToLongFunction<RecruitingKpiByUserVO> fieldGetter = getFieldGetter(x.getStageName(),applicationStatusType);
                if (fieldGetter == null) {
                    return false; // 未知的枚举值
                }

                Long fieldValue = fieldGetter.applyAsLong(vo);
                Long minValue = x.getMinValue() == null ? 0 : x.getMinValue();
                Long maxValue = x.getMaxValue() == null ? 100000000 : x.getMaxValue();
                if (fieldValue < minValue || fieldValue > maxValue) {
                    return false;
                }
            }
        }
        if (null != stayedOverList && !stayedOverList.isEmpty()) {
            if (stayedOverList.contains(KpiReportByUserStayedOver.SEVENTY_TWO_HRS)) {
                Integer value = applicationStatusType.equals(RecruitingKpiApplicationStatusType.ALL) ? vo.getStayedOver72Hrs() : vo.getCurrentStayedOver72Hrs();
                Long num = applicationStatusType.equals(RecruitingKpiApplicationStatusType.ALL) ? vo.getSubmitToClientNum() : vo.getSubmitToClientCurrentNum();
                if (null == value || null == num) {
                    return false;
                }
            }
            if (stayedOverList.contains(KpiReportByUserStayedOver.TWENTY_FOUR_HRS)) {
                Integer value = applicationStatusType.equals(RecruitingKpiApplicationStatusType.ALL) ? vo.getStayedOver24Hrs() : vo.getCurrentStayedOver24Hrs();
                Long num = applicationStatusType.equals(RecruitingKpiApplicationStatusType.ALL) ? vo.getSubmitToJobNum() : vo.getSubmitToJobCurrentNum();
                if (null == value || null == num) {
                    return false;
                }
            }
        }
        return true;
    }


    protected void appendEmptyUsersData(List<RecruitingKpiByUserVO> voList, RecruitingKpiReportSearchDto searchDto, List<UserTeam> allUsers){
        if (allUsers.isEmpty()) {
            return;
        }
        List<Long> allUserIds = allUsers.stream().map(UserTeam::getUserId).collect(Collectors.toList());
        List<Long> filterUserIds = userService.getUserIdFilterByTeamCategory(allUserIds).getBody();
        allUsers = allUsers.stream().filter(t -> filterUserIds.contains(t.getUserId())).collect(Collectors.toList());
        if (allUsers.isEmpty()){
            return;
        }
        if (searchDto.getGroupByFieldList().stream().anyMatch(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains)){
            // 生成完整的时间序列
            List<String> completeDateList = generateCompleteDateList(searchDto);
            Map<String, Set<Long>> dateToUsers = voList.stream().collect(Collectors.groupingBy(RecruitingKpiCommonCountVO::getGroupByDate, Collectors.mapping(RecruitingKpiCommonCountVO::getUserId, Collectors.toSet())));

            List<UserTeam> finalAllUsers = allUsers;
            // 遍历完整的时间序列，确保每个时间点都有所有用户的数据
            completeDateList.forEach(date -> {
                Set<Long> userIdSet = dateToUsers.computeIfAbsent(date, k -> new HashSet<>());
                finalAllUsers.forEach(userTeam -> {
                    if (!userIdSet.contains(userTeam.getUserId())){
                        RecruitingKpiByUserVO recruitingKpiByUserVO = new RecruitingKpiByUserVO();
                        recruitingKpiByUserVO.setUserId(userTeam.getUserId());
                        recruitingKpiByUserVO.setUserName(userTeam.getUserName());
                        recruitingKpiByUserVO.setTeamId(userTeam.getTeamId());
                        recruitingKpiByUserVO.setTeamName(userTeam.getTeamName());
                        recruitingKpiByUserVO.setGroupByDate(date);
                        recruitingKpiByUserVO.setIsLeaf(true);
                        voList.add(recruitingKpiByUserVO);
                        userIdSet.add(userTeam.getUserId());
                    }
                });
            });
        }else {
            Set<Long> existUserIds = voList.stream().map(RecruitingKpiByUserVO::getUserId).collect(Collectors.toSet());
            allUsers.forEach(userTeam -> {
                if (!existUserIds.contains(userTeam.getUserId())){
                    RecruitingKpiByUserVO recruitingKpiByUserVO = new RecruitingKpiByUserVO();
                    recruitingKpiByUserVO.setUserId(userTeam.getUserId());
                    recruitingKpiByUserVO.setUserName(userTeam.getUserName());
                    recruitingKpiByUserVO.setTeamId(userTeam.getTeamId());
                    recruitingKpiByUserVO.setTeamName(userTeam.getTeamName());
                    recruitingKpiByUserVO.setIsLeaf(true);
                    voList.add(recruitingKpiByUserVO);
                    existUserIds.add(userTeam.getUserId());
                }
            });
        }
    }


    protected void appendEmptyTeamsData(List<RecruitingKpiByUserVO> voList, RecruitingKpiReportSearchDto searchDto, List<TeamIdName> allTeams){
        if (allTeams.isEmpty()) {
            return;
        }
        if (searchDto.getGroupByFieldList().stream().anyMatch(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains)){
            // 生成完整的时间序列
            List<String> completeDateList = generateCompleteDateList(searchDto);
            Map<String, Set<Long>> dateToTeams = voList.stream().collect(Collectors.groupingBy(RecruitingKpiCommonCountVO::getGroupByDate, Collectors.mapping(RecruitingKpiCommonCountVO::getTeamId, Collectors.toSet())));

            // 遍历完整的时间序列，确保每个时间点都有所有团队的数据
            completeDateList.forEach(date -> {
                Set<Long> teamIdSet = dateToTeams.computeIfAbsent(date, k -> new HashSet<>());
                allTeams.forEach(teamIdName -> {
                    if (!teamIdSet.contains(teamIdName.getId())){
                        RecruitingKpiByUserVO recruitingKpiByUserVO = new RecruitingKpiByUserVO();
                        recruitingKpiByUserVO.setTeamId(teamIdName.getId());
                        recruitingKpiByUserVO.setTeamName(teamIdName.getName());
                        recruitingKpiByUserVO.setTeamLevel(teamIdName.getTeamLevel());
                        recruitingKpiByUserVO.setTeamParentId(teamIdName.getParentId());
                        recruitingKpiByUserVO.setIsLeaf(false);
                        recruitingKpiByUserVO.setGroupByDate(date);
                        voList.add(recruitingKpiByUserVO);
                        teamIdSet.add(teamIdName.getId());
                    }
                });
            });
        }else {
            Set<Long> existTeamIds = voList.stream().map(RecruitingKpiByUserVO::getTeamId).collect(Collectors.toSet());
            allTeams.forEach(teamIdName -> {
                if (!existTeamIds.contains(teamIdName.getId())){
                    RecruitingKpiByUserVO recruitingKpiByUserVO = new RecruitingKpiByUserVO();
                    recruitingKpiByUserVO.setTeamId(teamIdName.getId());
                    recruitingKpiByUserVO.setTeamName(teamIdName.getName());
                    recruitingKpiByUserVO.setTeamLevel(teamIdName.getTeamLevel());
                    recruitingKpiByUserVO.setTeamParentId(teamIdName.getParentId());
                    recruitingKpiByUserVO.setIsLeaf(false);
                    voList.add(recruitingKpiByUserVO);
                    existTeamIds.add(teamIdName.getId());
                }
            });
        }
    }

    /**
     * 根据时间维度生成完整的时间序列
     * @param searchDto 搜索条件
     * @return 完整的时间序列列表
     */
    private List<String> generateCompleteDateList(RecruitingKpiReportSearchDto searchDto) {
        List<String> dateList = new ArrayList<>();
        String startDate = searchDto.getStartDate();
        String endDate = searchDto.getEndDate();

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            return dateList;
        }

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        // 获取时间维度类型
        RecruitingKpiGroupByFieldType timeDimension = searchDto.getGroupByFieldList().stream()
                .filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains)
                .findFirst()
                .orElse(null);

        if (timeDimension == null) {
            return dateList;
        }

        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy");

        switch (timeDimension) {
            case DAY:
                // 按天生成：从开始日期到结束日期的每一天
                LocalDate currentDay = start;
                while (!currentDay.isAfter(end)) {
                    dateList.add(currentDay.format(dayFormatter));
                    currentDay = currentDay.plusDays(1);
                }
                break;

            case WEEK:
                // 按周生成：获取开始日期所在周的周一，然后按周递增
                LocalDate weekStart = start.with(TemporalAdjusters.previousOrSame(WeekFields.ISO.getFirstDayOfWeek()));
                LocalDate weekEnd = end.with(TemporalAdjusters.nextOrSame(WeekFields.ISO.getFirstDayOfWeek().minus(1)));

                LocalDate currentWeek = weekStart;
                while (!currentWeek.isAfter(weekEnd)) {
                    dateList.add(currentWeek.format(dayFormatter));
                    currentWeek = currentWeek.plusWeeks(1);
                }
                break;

            case MONTH:
                // 按月生成：获取开始日期所在月的第一天，然后按月递增
                LocalDate monthStart = start.with(TemporalAdjusters.firstDayOfMonth());
                LocalDate monthEnd = end.with(TemporalAdjusters.firstDayOfMonth());

                LocalDate currentMonth = monthStart;
                while (!currentMonth.isAfter(monthEnd)) {
                    dateList.add(currentMonth.format(monthFormatter));
                    currentMonth = currentMonth.plusMonths(1);
                }
                break;

            case QUARTER:
                // 按季度生成：返回 年-季度 格式，比如 2025-3 表示2025年Q3
                LocalDate quarterStart = start.with(start.getMonth().firstMonthOfQuarter()).with(TemporalAdjusters.firstDayOfMonth());
                LocalDate quarterEnd = end.with(end.getMonth().firstMonthOfQuarter()).with(TemporalAdjusters.firstDayOfMonth());

                LocalDate currentQuarter = quarterStart;
                while (!currentQuarter.isAfter(quarterEnd)) {
                    int year = currentQuarter.getYear();
                    int quarter = (currentQuarter.getMonthValue() - 1) / 3 + 1; // 计算季度：1-4
                    dateList.add(year + "-" + quarter);
                    currentQuarter = currentQuarter.plusMonths(3);
                }
                break;

            case YEAR:
                // 按年生成：获取开始日期所在年的第一天，然后按年递增
                LocalDate yearStart = start.with(TemporalAdjusters.firstDayOfYear());
                LocalDate yearEnd = end.with(TemporalAdjusters.firstDayOfYear());

                LocalDate currentYear = yearStart;
                while (!currentYear.isAfter(yearEnd)) {
                    dateList.add(currentYear.format(yearFormatter));
                    currentYear = currentYear.plusYears(1);
                }
                break;

            default:
                break;
        }

        return dateList;
    }


    private long safeLong(Long value) {
        return value == null ? 0L : value;
    }

    protected static final Map<ReportApplicationStatus,Class<?>> APPLICATION_EXCEL_CLASS_MAP = new EnumMap<>(ReportApplicationStatus.class);
    static {
        // 提交到职位相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.SUBMIT_TO_JOB, RecruitingKpiSubmitToJobDetailExcelENVO.class);
        // 提交给客户相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.SUBMIT_TO_CLIENT, RecruitingKpiSubmitToClientDetailExcelENVO.class);
        // 面试相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.INTERVIEW, RecruitingKpiInterviewDetailExcelENVO.class);
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.INTERVIEW_FIRST, RecruitingKpiInterviewDetailExcelENVO.class);
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.INTERVIEW_SECOND, RecruitingKpiInterviewDetailExcelENVO.class);
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.INTERVIEW_FINAL, RecruitingKpiInterviewDetailExcelENVO.class);
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.TWO_OR_MORE_INTERVIEW, RecruitingKpiInterviewDetailExcelENVO.class);
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.INTERVIEW_APPOINTMENTS, RecruitingKpiInterviewDetailExcelENVO.class);
        // Offer相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.OFFER, RecruitingKpiOfferDetailExcelENVO.class);
        // Offer接受相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.OFFER_ACCEPT, RecruitingKpiOfferAcceptDetailExcelENVO.class);
        // 入职相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.ON_BOARD, RecruitingKpiOnboardDetailExcelENVO.class);
        // 淘汰相关
        APPLICATION_EXCEL_CLASS_MAP.put(ReportApplicationStatus.ELIMINATED, RecruitingKpiEliminateDetailExcelENVO.class);
    }

    protected CompletableFuture<Map<Long, UserBriefDTO>> getUserMapFuture() {
        SecurityContext context = SecurityContextHolder.getContext();
        return CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return userService.getAllBriefUsersByTenantId(SecurityUtils.getTenantId()).getBody().stream().collect(Collectors.toMap(UserBriefDTO::getId, a -> a, (a1, a2) -> a1));
        });
    }

    public List<RecruitingKpiByCompanyVO> contractorCompanyVoList(Set<String> keySet, RecruitingKpiReportSearchDto searchDto) {
        return keySet.parallelStream().map(key -> {
            RecruitingKpiByCompanyVO vo = new RecruitingKpiByCompanyVO();
            setGroupByField(vo, key, searchDto.getGroupByFieldList());
            vo.setKey(key);
            return vo;
        }).collect(Collectors.toList());
    }

    public CompletableFuture<ConcurrentMap<Long, Long>> getBdReportProgressNoteStats(List<Long> companyVoList, RecruitingKpiReportSearchDto searchDto) {
        SecurityContext context = SecurityContextHolder.getContext();
        return CompletableFuture.supplyAsync(() -> {
            if (BooleanUtil.isTrue(searchDto.isXxlJobFlag())) {
                return new ConcurrentHashMap<>();
            }
            if(null == companyVoList || companyVoList.isEmpty()){
                return new ConcurrentHashMap<>();
            }
            SecurityContextHolder.setContext(context);
            String url = crmUrl + "/report/api/v1/bd-report/progress-notes/stats";
            log.info("[apn getBdReportProgressNoteStats {}] url = {}", SecurityUtils.getUserId(), url);
            Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("companyIdList", companyVoList);
            jsonObject.put("tenantId", searchDto.getSearchTenantId());
            jsonObject.put("timeZone", searchDto.getTimezone());
            /*if(null != searchDto.getJob() && CollUtil.isNotEmpty(searchDto.getJob().getTypeList())){
                jsonObject.put("serviceType",searchDto.getJob().getTypeList().stream().map(JobType::getName).toList());
            }*/

            HttpResponse response;
            try {
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                response = httpService.post(url, headers, jsonObject.toString());
                stopWatch.stop();
                log.info("[apn ] by company kpi getBdReportProgressNoteStats time = {}ms", stopWatch.getTotalTimeMillis());
            } catch (IOException e) {
                log.error("[apn searchRecruitingKpiReportByCompany {}] search bd report progress notes stats is error , message = {}", searchDto.getSearchUserId(), ExceptionUtil.getAllExceptionMsg(e));
                throw new RuntimeException(e);
            }
            if (Objects.equals(response.getCode(), 200)) {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), BDReportProgressNoteStatsVO.class)
                        .stream().collect(Collectors.toConcurrentMap(BDReportProgressNoteStatsVO::getCompanyId, BDReportProgressNoteStatsVO::getCount));
            }
            log.error("[apn searchRecruitingKpiReportByCompany {}] search bd report progress notes stats is error , code = {}, msg = {}", searchDto.getSearchUserId(), response.getCode(), response.getBody());
            return new ConcurrentHashMap<>();
        });
    }

    public void setCompanyInfo(RecruitingKpiByCompanyVO vo, Map<Long, List<KpiReportCompanyInfoVO>> companyIdInfoMap,
                                ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>> twoWeekMap,
                                Long jobId) {
        List<KpiReportCompanyInfoVO> kpiReportCompanyInfoVOList = companyIdInfoMap.get(vo.getCompanyId());
        if (CollUtil.isNotEmpty(kpiReportCompanyInfoVOList)) {
            vo.setAmList(kpiReportCompanyInfoVOList.stream().filter(infoVo -> infoVo.getSalesLeadRole().equals(SalesLeadRoleType.ACCOUNT_MANAGER)).distinct().toList());
            vo.setSalesLeadList(kpiReportCompanyInfoVOList.stream().filter(infoVo -> infoVo.getSalesLeadRole().equals(SalesLeadRoleType.SALES_LEAD_OWNER)).distinct().toList());
            vo.setBdOwnerList(kpiReportCompanyInfoVOList.stream().filter(infoVo -> infoVo.getSalesLeadRole().equals(SalesLeadRoleType.BUSINESS_DEVELOPMENT)).distinct().toList());
            vo.setCoAmList(kpiReportCompanyInfoVOList.stream().filter(infoVo -> infoVo.getSalesLeadRole().equals(SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER)).distinct().toList());
            if (CollUtil.isNotEmpty(kpiReportCompanyInfoVOList)) {
                vo.setCompanyCreatedDate(kpiReportCompanyInfoVOList.get(0).getCompanyCreatedDate());
                vo.setCountry(kpiReportCompanyInfoVOList.get(0).getCountry());
                vo.setCompanyNoteCount(kpiReportCompanyInfoVOList.get(0).getCompanyNoteCount());
            }
            vo.setIndustries(kpiReportCompanyInfoVOList.get(0).getIndustries());
            vo.setClientStatus(kpiReportCompanyInfoVOList.get(0).getActive().name());
            vo.setRequestDate(kpiReportCompanyInfoVOList.get(0).getRequestDate());
        }
        if (CollUtil.isNotEmpty(twoWeekMap)) {
            if (twoWeekMap.containsKey(vo.getCompanyId())) {
                List<KpiReportCompanySubmitToClientTwoWeekVO> twoWeekVO = twoWeekMap.get(vo.getCompanyId());
                if (jobId == null) {
                    vo.setLastWeek(twoWeekVO.get(0).getLastWeek());
                    vo.setThisWeek(twoWeekVO.get(0).getThisWeek());
                    vo.setLastWeekCount(twoWeekVO.get(0).getLastWeekCount());
                    vo.setThisWeekCount(twoWeekVO.get(0).getThisWeekCount());
                    vo.setThisWeekCurrentCountNum(twoWeekVO.get(0).getThisCurrentCountNum());
                    vo.setLastWeekCurrentCountNum(twoWeekVO.get(0).getLastCurrentCountNum());
                    if (twoWeekVO.size() > 1) {
                        Optional<KpiReportCompanySubmitToClientTwoWeekVO> thisWeek = twoWeekVO.stream().filter(x -> StringUtils.isNotBlank(x.getThisWeek()))
                                .filter(x -> null != x.getThisWeekCountAiRecommendNum()).findFirst();
                        if (thisWeek.isPresent()) {
                            vo.setThisWeekCount(thisWeek.get().getThisWeekCount());
                            vo.setThisWeekCurrentCountNum(thisWeek.get().getThisCurrentCountNum());
                            vo.setThisWeekCountAIRecommend(thisWeek.get().getThisWeekCountAiRecommendNum());
                            vo.setThisWeekCurrentCountNumAIRecommend(thisWeek.get().getThisCurrentCountNumAiRecommendNum());
                            vo.setThisWeekCountPrecisionAIRecommend(thisWeek.get().getThisWeekCountPrecisionAiRecommendNum());
                            vo.setThisWeekCurrentCountNumPrecisionAIRecommend(thisWeek.get().getThisCurrentCountNumPrecisionAiRecommendNum());
                        }
                        Optional<KpiReportCompanySubmitToClientTwoWeekVO> lastWeek = twoWeekVO.stream().filter(x -> StringUtils.isNotBlank(x.getLastWeek()))
                                .filter(x -> null != x.getLastWeekCountAiRecommendNum()).findFirst();
                        if (lastWeek.isPresent()) {
                            vo.setLastWeekCount(lastWeek.get().getLastWeekCount());
                            vo.setLastWeekCurrentCountNum(lastWeek.get().getLastCurrentCountNum());
                            vo.setLastWeekCountAIRecommend(lastWeek.get().getLastWeekCountAiRecommendNum());
                            vo.setLastWeekCurrentCountNumAIRecommend(lastWeek.get().getLastCurrentCountNumAiRecommendNum());
                            vo.setLastWeekCountPrecisionAIRecommend(lastWeek.get().getLastWeekCountPrecisionAiRecommendNum());
                            vo.setLastWeekCurrentCountNumPrecisionAIRecommend(lastWeek.get().getLastCurrentCountNumPrecisionAiRecommendNum());
                        }
                    } else {
                        setAiThisWeekAndLastWeekRecommend(vo,twoWeekVO);
                    }
                } else {
                    if (jobId.equals(twoWeekVO.get(0).getJobId())) {
                        vo.setLastWeek(twoWeekVO.get(0).getLastWeek());
                        vo.setThisWeek(twoWeekVO.get(0).getThisWeek());
                        vo.setLastWeekCount(twoWeekVO.get(0).getLastWeekCount());
                        vo.setThisWeekCount(twoWeekVO.get(0).getThisWeekCount());
                        vo.setThisWeekCurrentCountNum(twoWeekVO.get(0).getThisCurrentCountNum());
                        vo.setLastWeekCurrentCountNum(twoWeekVO.get(0).getLastCurrentCountNum());
                        setAiThisWeekAndLastWeekRecommend(vo,twoWeekVO);
                    }
                }
            }
        }
    }

    public void setThisWeekAndLastWeek(RecruitingKpiByCompanyVO vo,List<KpiReportCompanySubmitToClientTwoWeekVO> twoWeekVO){
        vo.setLastWeek(twoWeekVO.get(0).getLastWeek());
        vo.setThisWeek(twoWeekVO.get(0).getThisWeek());
        vo.setLastWeekCount(twoWeekVO.get(0).getLastWeekCount());
        vo.setThisWeekCount(twoWeekVO.get(0).getThisWeekCount());
        vo.setThisWeekCurrentCountNum(twoWeekVO.get(0).getThisCurrentCountNum());
        vo.setLastWeekCurrentCountNum(twoWeekVO.get(0).getLastCurrentCountNum());
    }
    public void setAiThisWeekAndLastWeekRecommend(RecruitingKpiByCompanyVO vo,List<KpiReportCompanySubmitToClientTwoWeekVO> twoWeekVO){
        vo.setThisWeekCountAIRecommend(twoWeekVO.get(0).getThisWeekCountAiRecommendNum());
        vo.setLastWeekCountAIRecommend(twoWeekVO.get(0).getLastWeekCountAiRecommendNum());
        vo.setThisWeekCurrentCountNumAIRecommend(twoWeekVO.get(0).getThisCurrentCountNumAiRecommendNum());
        vo.setLastWeekCurrentCountNumAIRecommend(twoWeekVO.get(0).getLastCurrentCountNumAiRecommendNum());
        vo.setThisWeekCountPrecisionAIRecommend(twoWeekVO.get(0).getThisWeekCountPrecisionAiRecommendNum());
        vo.setLastWeekCountPrecisionAIRecommend(twoWeekVO.get(0).getLastWeekCountPrecisionAiRecommendNum());
        vo.setThisWeekCurrentCountNumPrecisionAIRecommend(twoWeekVO.get(0).getThisCurrentCountNumPrecisionAiRecommendNum());
        vo.setLastWeekCurrentCountNumPrecisionAIRecommend(twoWeekVO.get(0).getLastCurrentCountNumPrecisionAiRecommendNum());
    }

    public Pair<List<RecruitingKpiByCompanyVO>,List<Long>> saveCompanyId(Set<String> keySet, RecruitingKpiReportSearchDto searchDto){
        List<RecruitingKpiByCompanyVO> companyVoList = contractorCompanyVoList(keySet, searchDto);
        //补全companyIdList
        List<Long> companyIdList = companyVoList.stream().map(RecruitingKpiByCompanyVO::getCompanyId).collect(Collectors.toList());
        SearchKpiCompanyDto companyDto = new SearchKpiCompanyDto();
        companyDto.setIdList(companyIdList);
        searchDto.setCompany(companyDto);

        return new Pair<>(companyVoList,companyIdList);
    }

    //获取jobNote中的companyId
    public List<Long> getCompanyIdByJobNote(ConcurrentMap<String, RecruitingKpiCommonCountVO> jobNoteMap){
        return jobNoteMap.values().stream().map(RecruitingKpiCommonCountVO::getCompanyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
