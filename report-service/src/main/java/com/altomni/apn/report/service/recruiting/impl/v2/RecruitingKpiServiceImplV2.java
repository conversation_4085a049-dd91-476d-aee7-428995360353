package com.altomni.apn.report.service.recruiting.impl.v2;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumIndustry;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.SearchKpiCompanyDto;
import com.altomni.apn.common.dto.recruiting.StageKpiReportDto;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiViewType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.recruiting.*;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCreatedVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiApplicationCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import com.altomni.apn.report.config.excel.CustomCellWriteWeightConfig;
import com.altomni.apn.report.domain.vo.RecruitingKpiByCompanyExcelVo;
import com.altomni.apn.report.domain.vo.RecruitingKpiByUserExcelVo;
import com.altomni.apn.report.domain.vo.TeamIdName;
import com.altomni.apn.report.domain.vo.UserTeam;
import com.altomni.apn.report.repository.v2.RecruitingKpiCompanyRepositoryV2;
import com.altomni.apn.report.repository.v2.RecruitingKpiUserRepositoryV2;
import com.altomni.apn.report.service.recruiting.RecruitingKpiService;
import com.altomni.apn.report.service.recruiting.impl.RecruitingKpiBaseServiceImpl;
import com.altomni.apn.report.util.ExcelUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service("recruitingKpiServiceV2")
public class RecruitingKpiServiceImplV2 extends RecruitingKpiBaseServiceImpl implements RecruitingKpiService {

    @Resource
    private RecruitingKpiUserRepositoryV2 recruitingKpiUserRepositoryV2;

    @Resource
    private RecruitingKpiCompanyRepositoryV2 recruitingKpiCompanyRepositoryV2;

    @Resource
    EnumCommonService commonService;

    /**
     * 查询用户的 KPI 数据，仅在以下三种情况会真正查询数据
     * 1. 用户是个人数据权限，只查询个人数据
     * 2. 有用户搜索时，按照用户维度查询搜索的用户的数据
     */
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserV2(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<Long> userIdList = searchDto.getUserIdList() != null ? new ArrayList<>(searchDto.getUserIdList()) : new ArrayList<>();
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
            userIdList = Collections.singletonList(SecurityUtils.getUserId());
        }
        // 没有用户搜索，不进行查询直接反返回空列表
        if (CollectionUtils.isEmpty(userIdList)) {
            log.info("[apn @{}] search recruiting kpi report by user, userIdList = {}, parent = null, return empty list", SecurityUtils.getUserId(), userIdList);
            return Collections.emptyList();
        }

        Collection<Boolean> activeStatus = List.of(true, false);
        if (ObjectUtil.isNotEmpty(searchDto.getUser().getUserActiveStatus())){
            activeStatus = List.of(searchDto.getUser().getUserActiveStatus());
        }
        List<UserTeam> searchUsesrs= reportRepository.findUsersByUserIds(userIdList, SecurityUtils.getTenantId(), activeStatus);

        RecruitingKpiReportSearchDto search = searchDto.deepCopy();
        search.setUserIdList(userIdList);
        Optional<RecruitingKpiGroupByFieldType> timeDimOpt = search.getGroupByFieldList().stream().filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findAny();
        List<RecruitingKpiGroupByFieldType> groupByField = switch (search.getViewType()) {
            case TEAM -> Stream.concat( timeDimOpt.stream(),Stream.of(RecruitingKpiGroupByFieldType.USER)).toList();
            case TIMELINE -> Stream.concat(timeDimOpt.stream(),Stream.of(RecruitingKpiGroupByFieldType.USER)).toList();
            default -> throw new IllegalStateException("Unexpected value: " + search.getViewType());
        };
        search.setGroupByFieldList(groupByField);
        List<RecruitingKpiByUserVO> voList = searchRecruitingKpiReportByUser(search, teamDTO);
        // 补全没有数据的用户
        appendEmptyUsersData(voList, searchDto, searchUsesrs);

        List<RecruitingKpiByUserVO> resultVoList = new ArrayList<>();
        List<Long> userIds = voList.stream().map(RecruitingKpiCommonCountVO::getUserId).filter(Objects::nonNull).toList();
        Map<Long, TeamInfoVO> userTeamMap = userService.getTeamInfoVOsByUserIds(userIds).getBody()
                .stream().collect(Collectors.toMap(TeamInfoVO::getUserId, Function.identity()));
        // 结果过滤
        List<StageKpiReportDto> stageSearchFilter = addStageFilter(search);
        voList.forEach(vo -> {
            TeamInfoVO userTeam = userTeamMap.getOrDefault(vo.getUserId(), new TeamInfoVO());
            vo.setTeamParentId(userTeam.getTeamId());
            vo.setTeamId(userTeam.getTeamId());
            vo.setTeamName(userTeam.getTeamName());
            vo.setIsLeaf(true);
            if (checkSearchFilter(vo, stageSearchFilter, search.getApplicationStatusType(), search.getStayedOverList())) {
                resultVoList.add(vo);
            }
        });
        return resultVoList;
    }

    /**
     * 以团队为维度，查询团队的 KPI 数据，每个团队会包含其下属所有子团队的数据
     * 1. 个人数据权限。不允许查询团队数据
     * 2. 有团队搜索，按照搜索的团队查询
     * 3. 无团队搜索，查询所有一级团队的数据
     * 4. 有用户搜索，搜索用户所在团队的数据
     */
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByTeam(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        // 个人权限时，不允许查询团队数据
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
            return Collections.emptyList();
        }
        RecruitingKpiReportSearchDto search = searchDto.deepCopy();

        List<Long> teamIds = searchDto.getTeamIdList();
        List<Long> userIds = searchDto.getUserIdList();
        List<TeamIdName> searchTeams = new ArrayList<>();

        Long parentId = Boolean.TRUE.equals(teamDTO.getAll()) ? Long.valueOf(-1L) : SecurityUtils.getTeamId();
        if (CollectionUtils.isEmpty(teamIds) && !CollectionUtils.isEmpty(userIds)) {
            // 只有用户搜索
            searchTeams = reportRepository.findAllRootTeamPathByUser(SecurityUtils.getTenantId(), userIds, parentId);
        } else if (CollectionUtils.isEmpty(userIds) && !CollectionUtils.isEmpty(teamIds)) {
            // 只有团队搜索
            searchTeams = reportRepository.findAllRootTeamPathByTeam(SecurityUtils.getTenantId(), teamIds, parentId);
        } else if (!CollectionUtils.isEmpty(userIds) && !CollectionUtils.isEmpty(teamIds)) {
            // 既有用户搜索，又有团队搜索
            List<TeamIdName> rootTeamByUser = reportRepository.findAllRootTeamPathByUser(SecurityUtils.getTenantId(), userIds, parentId);
            List<TeamIdName> rootTeamByTeam = reportRepository.findAllRootTeamPathByTeam(SecurityUtils.getTenantId(), teamIds, parentId);
            // 根据 team id 去重
            Map<Long, TeamIdName> rootTeamMap = Stream.concat(rootTeamByTeam.stream(), rootTeamByUser.stream())
                    .collect(Collectors.toMap(TeamIdName::getId, Function.identity(), (t1, t2) -> t1));
            searchTeams.addAll(rootTeamMap.values());
        } else {
            // 无用户搜索，无团队搜索
            if (Boolean.TRUE.equals(teamDTO.getAll())) {
                // 全部数据权限
                // 查询所有根团队
                searchTeams = reportRepository.findAllRootTeam(SecurityUtils.getTenantId());
            } else {
                // 团队数据权限
                // 查询搜索用户的团队作为根团队
                searchTeams = reportRepository.findTeamsByUserIds(List.of(SecurityUtils.getUserId()), SecurityUtils.getTenantId());
            }
        }
        search.setTeamIdList(searchTeams.stream().map(TeamIdName::getId).toList());
        search.setUserIdList(List.of());
        Optional<RecruitingKpiGroupByFieldType> timeDimOpt = search.getGroupByFieldList().stream().filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findAny();
        List<RecruitingKpiGroupByFieldType> groupByField = switch (search.getViewType()) {
            case TEAM -> Stream.concat(Stream.of(RecruitingKpiGroupByFieldType.TEAM), timeDimOpt.stream()).toList();
            case TIMELINE ->
                    Stream.concat(timeDimOpt.stream(), Stream.of(RecruitingKpiGroupByFieldType.TEAM)).toList();
            default -> throw new IllegalStateException("Unexpected value: " + search.getViewType());
        };
        search.setGroupByFieldList(groupByField.isEmpty() ? List.of(RecruitingKpiGroupByFieldType.TEAM) : groupByField);
        List<RecruitingKpiByUserVO> voList = searchRecruitingKpiReportByUser(search, teamDTO);
        // 把没有数据的团队补全
        appendEmptyTeamsData(voList, searchDto, searchTeams);
        return voList;
    }

    /**
     * 下钻获取团队数据
     */
    public List<RecruitingKpiByUserVO> drillRecruitingKpiReportByTeam(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        List<TeamIdName> childTeamIdList = reportRepository.findTeamsByParentTeamId(searchDto.getParentId(), SecurityUtils.getTenantId());
        // 没有子团队了
        if (childTeamIdList.isEmpty()) {
            return Collections.emptyList();
        }
        RecruitingKpiReportSearchDto search = searchDto.deepCopy();
        search.setTeamIdList(childTeamIdList.stream().map(TeamIdName::getId).toList());
        Optional<RecruitingKpiGroupByFieldType> timeDimOpt = search.getGroupByFieldList().stream().filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findAny();
        List<RecruitingKpiGroupByFieldType> groupByField = switch (search.getViewType()) {
            case TEAM -> Stream.concat(Stream.of(RecruitingKpiGroupByFieldType.TEAM), timeDimOpt.stream()).toList();
            case TIMELINE -> Stream.concat(timeDimOpt.stream(),Stream.of(RecruitingKpiGroupByFieldType.TEAM)).toList();
            default -> throw new IllegalStateException("Unexpected value: " + search.getViewType());
        };
        search.setGroupByFieldList(groupByField.isEmpty() ? List.of(RecruitingKpiGroupByFieldType.TEAM) : groupByField);
        List<RecruitingKpiByUserVO> voList = searchRecruitingKpiReportByUser(search, teamDTO);
        // 只补全下钻的团队
        appendEmptyTeamsData(voList, searchDto, childTeamIdList);
        return voList;
    }

    /**
     * 下钻获取用户
     */
    public List<RecruitingKpiByUserVO> drillRecruitingKpiReportByUserV2(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        Collection<Boolean> activeStatus = List.of(true, false);
        if (ObjectUtil.isNotEmpty(searchDto.getUser().getUserActiveStatus())){
            activeStatus = List.of(searchDto.getUser().getUserActiveStatus());
        }

        List<UserTeam> childUsers = reportRepository.findUsersByTeamIds(List.of(searchDto.getParentId()), SecurityUtils.getTenantId(), activeStatus);

        // 团队下没有用户
        if (childUsers.isEmpty()) {
            return Collections.emptyList();
        }
        RecruitingKpiReportSearchDto search = searchDto.deepCopy();

        search.setUserIdList(childUsers.stream().map(UserTeam::getUserId).toList());

        Optional<RecruitingKpiGroupByFieldType> timeDimOpt = search.getGroupByFieldList().stream().filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findAny();
        List<RecruitingKpiGroupByFieldType> groupByField = switch (search.getViewType()) {
            case TEAM -> Stream.concat( timeDimOpt.stream(),Stream.of(RecruitingKpiGroupByFieldType.USER)).toList();
            case TIMELINE -> Stream.concat(timeDimOpt.stream(),Stream.of(RecruitingKpiGroupByFieldType.USER)).toList();
            default -> throw new IllegalStateException("Unexpected value: " + search.getViewType());
        };
        search.setGroupByFieldList(groupByField);
        List<RecruitingKpiByUserVO> voList = searchRecruitingKpiReportByUser(search, teamDTO);
        appendEmptyUsersData(voList, searchDto, childUsers);

        List<RecruitingKpiByUserVO> resultVoList = new ArrayList<>();
        List<Long> userIds = voList.stream().map(RecruitingKpiCommonCountVO::getUserId).filter(Objects::nonNull).toList();
        Map<Long, TeamInfoVO> userTeamMap = userService.getTeamInfoVOsByUserIds(userIds).getBody()
                .stream().collect(Collectors.toMap(TeamInfoVO::getUserId, Function.identity()));
        // 结果过滤
        List<StageKpiReportDto> stageSearchFilter = addStageFilter(search);
        voList.forEach(vo -> {
            TeamInfoVO userTeam = userTeamMap.getOrDefault(vo.getUserId(), new TeamInfoVO());
            vo.setTeamParentId(userTeam.getTeamId());
            vo.setTeamId(userTeam.getTeamId());
            vo.setTeamName(userTeam.getTeamName());
            vo.setIsLeaf(true);
            if (checkSearchFilter(vo, stageSearchFilter, search.getApplicationStatusType(), search.getStayedOverList())) {
                resultVoList.add(vo);
            }
        });
        return resultVoList;
    }


    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        List<RecruitingKpiByUserVO> teamData = searchRecruitingKpiReportByTeam(searchDto, teamDTO);
        List<RecruitingKpiByUserVO> userData = searchRecruitingKpiReportByUserV2(searchDto, teamDTO);
        List<RecruitingKpiByUserVO> voList = Stream.of(teamData, userData).flatMap(List::stream).peek(RecruitingKpiCommonCountVO::assignIdAndParentId)
                .toList();
        // 个人数据权限或者数据只有0 或1 条，直接返回，没有树结构
        if (Boolean.TRUE.equals(teamDTO.getSelf()) || voList.size() < 2) {
            return voList;
        }

        return buildKpiTreeStructure(voList, searchDto);
    }

    /**
     * 构建KPI报告的树结构
     * @param voList 原始数据列表
     * @param searchDto 搜索条件
     * @return 树结构根节点列表
     */
    public List<RecruitingKpiByUserVO> buildKpiTreeStructure(List<RecruitingKpiByUserVO> voList, RecruitingKpiReportSearchDto searchDto) {
        List<RecruitingKpiByUserVO> treeRootVoList;

        switch (searchDto.getViewType()) {
            case TEAM -> {
                if (searchDto.getGroupByFieldList().stream().anyMatch(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains)) {
                    treeRootVoList = buildTeamWithTimeTree(voList);
                } else {
                    treeRootVoList = buildTeamOnlyTree(voList);
                }
            }
            case TIMELINE -> {
                treeRootVoList = buildTimelineTree(voList);
            }
            default -> throw new CustomParameterizedException("Invalid view type");
        }

        return treeRootVoList;
    }

    /**
     * 构建团队+时间维度的树结构
     */
    private List<RecruitingKpiByUserVO> buildTeamWithTimeTree(List<RecruitingKpiByUserVO> voList) {
        // 按团队ID分组，找到每个团队的根节点（teamLevel最小的）
        Map<Long, RecruitingKpiByUserVO> rootTeamNodes = voList.stream()
            .filter(vo -> vo.getTeamLevel() != null && vo.getUserId() == null)
            .collect(Collectors.toMap(
                RecruitingKpiByUserVO::getTeamId,
                Function.identity(),
                (existing, replacement) -> existing.getTeamLevel() <= replacement.getTeamLevel() ? existing : replacement
            ));

        // 按时间分组所有数据
        Map<String, List<RecruitingKpiByUserVO>> dateGroups = voList.stream()
            .filter(vo -> vo.getGroupByDate() != null)
            .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getGroupByDate));

        List<RecruitingKpiByUserVO> result = new ArrayList<>();

        // 为每个根团队构建树
        for (RecruitingKpiByUserVO rootTeam : rootTeamNodes.values()) {
            // 创建根团队节点的副本，避免修改原对象
            RecruitingKpiByUserVO rootNode = cloneVoForTree(rootTeam);
            rootNode.setIsLeaf(false);
            rootNode.setChildren(new ArrayList<>());

            // 为每个时间维度创建子节点
            for (Map.Entry<String, List<RecruitingKpiByUserVO>> dateEntry : dateGroups.entrySet()) {
                String date = dateEntry.getKey();
                List<RecruitingKpiByUserVO> dateVoList = dateEntry.getValue();

                // 找到该时间下该团队的数据作为时间节点
                Optional<RecruitingKpiByUserVO> timeNodeOpt = dateVoList.stream()
                    .filter(vo -> rootTeam.getTeamId().equals(vo.getTeamId()))
                    .findFirst();

                if (timeNodeOpt.isPresent()) {
                    RecruitingKpiByUserVO timeNode = cloneVoForTree(timeNodeOpt.get());
                    timeNode.setIsLeaf(false);
                    timeNode.setParentId(rootNode.getId());
                    timeNode.setId(date + "-" + rootTeam.getTeamId());
                    timeNode.setChildren(buildTeamChildrenSafe(rootTeam.getTeamId(), date, dateVoList, timeNode.getId()));

                    // 只有当时间节点有子数据时才添加
                    if (!timeNode.getChildren().isEmpty()) {
                        rootNode.getChildren().add(timeNode);
                    }
                }
            }

            // 只有当根节点有子数据时才添加到结果中
            if (!rootNode.getChildren().isEmpty()) {
                result.add(rootNode);
            }
        }

        return result;
    }

    /**
     * 构建仅团队维度的树结构
     */
    private List<RecruitingKpiByUserVO> buildTeamOnlyTree(List<RecruitingKpiByUserVO> voList) {
        Map<Long, List<RecruitingKpiByUserVO>> teamGroups = voList.stream()
            .filter(vo -> vo.getTeamId() != null)
            .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getTeamId));

        // 找到团队节点
        Map<Long, RecruitingKpiByUserVO> teamNodes = new HashMap<>();
        for (Map.Entry<Long, List<RecruitingKpiByUserVO>> entry : teamGroups.entrySet()) {
            Long teamId = entry.getKey();
            RecruitingKpiByUserVO teamNode = entry.getValue().stream()
                .filter(vo -> vo.getUserId() == null) // 团队节点
                .findFirst()
                .orElse(entry.getValue().get(0)); // 如果没有团队节点，用第一个

            RecruitingKpiByUserVO clonedTeamNode = cloneVoForTree(teamNode);
            clonedTeamNode.setIsLeaf(false);
            clonedTeamNode.setChildren(buildTeamChildrenSafe(teamId, null, voList, clonedTeamNode.getId()));
            teamNodes.put(teamId, clonedTeamNode);
        }

        // 构建层级关系并找到根节点
        List<RecruitingKpiByUserVO> result = new ArrayList<>();
        Set<Long> childTeamIds = new HashSet<>();

        // 先标记所有子团队
        for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
            Long parentTeamId = teamNode.getTeamParentId();
            if (parentTeamId != null && teamNodes.containsKey(parentTeamId)) {
                childTeamIds.add(teamNode.getTeamId());
                teamNode.setParentId(parentTeamId.toString());
                teamNodes.get(parentTeamId).getChildren().add(teamNode);
            }
        }

        // 添加根团队到结果中
        for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
            if (!childTeamIds.contains(teamNode.getTeamId()) && !teamNode.getChildren().isEmpty()) {
                result.add(teamNode);
            }
        }

        return result;
    }

    /**
     * 构建时间线维度的树结构
     */
    private List<RecruitingKpiByUserVO> buildTimelineTree(List<RecruitingKpiByUserVO> voList) {
        Map<String, List<RecruitingKpiByUserVO>> dateGroups = voList.stream()
            .filter(vo -> vo.getGroupByDate() != null)
            .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getGroupByDate));

        List<RecruitingKpiByUserVO> result = new ArrayList<>();

        for (Map.Entry<String, List<RecruitingKpiByUserVO>> dateEntry : dateGroups.entrySet()) {
            String date = dateEntry.getKey();
            List<RecruitingKpiByUserVO> dateVoList = dateEntry.getValue();

            // 使用第一个VO作为时间根节点的模板
            RecruitingKpiByUserVO dateRootNode = cloneVoForTree(dateVoList.get(0));
            dateRootNode.setId(date);
            dateRootNode.setIsLeaf(false);
            dateRootNode.setChildren(new ArrayList<>());

            // 按团队分组并构建团队树
            Map<Long, List<RecruitingKpiByUserVO>> teamGroups = dateVoList.stream()
                .filter(vo -> vo.getTeamId() != null)
                .collect(Collectors.groupingBy(RecruitingKpiByUserVO::getTeamId));

            // 构建团队节点
            Map<Long, RecruitingKpiByUserVO> teamNodes = new HashMap<>();
            for (Map.Entry<Long, List<RecruitingKpiByUserVO>> teamEntry : teamGroups.entrySet()) {
                Long teamId = teamEntry.getKey();
                RecruitingKpiByUserVO teamNode = teamEntry.getValue().stream()
                    .filter(vo -> vo.getUserId() == null)
                    .findFirst()
                    .orElse(teamEntry.getValue().get(0));

                RecruitingKpiByUserVO clonedTeamNode = cloneVoForTree(teamNode);
                clonedTeamNode.setId(date + "-" + teamId);
                clonedTeamNode.setParentId(date);
                clonedTeamNode.setIsLeaf(false);
                clonedTeamNode.setChildren(buildTeamChildrenSafe(teamId, date, dateVoList, clonedTeamNode.getId()));
                teamNodes.put(teamId, clonedTeamNode);
            }

            // 构建团队层级关系并找到根团队
            Set<Long> childTeamIds = new HashSet<>();
            for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
                Long parentTeamId = teamNode.getTeamParentId();
                if (parentTeamId != null && teamNodes.containsKey(parentTeamId)) {
                    childTeamIds.add(teamNode.getTeamId());
                    teamNode.setParentId(date + "-" + parentTeamId);
                    teamNodes.get(parentTeamId).getChildren().add(teamNode);
                }
            }

            // 添加根团队到时间节点
            for (RecruitingKpiByUserVO teamNode : teamNodes.values()) {
                if (!childTeamIds.contains(teamNode.getTeamId())) {
                    dateRootNode.getChildren().add(teamNode);
                }
            }

            // 只有当时间节点有子数据时才添加
            if (!dateRootNode.getChildren().isEmpty()) {
                result.add(dateRootNode);
            }
        }

        // 按时间排序
        result.sort(Comparator.comparing(RecruitingKpiByUserVO::getGroupByDate, Comparator.nullsLast(String::compareTo)));
        return result;
    }

    /**
     * 克隆VO对象用于树结构，避免修改原对象
     */
    private RecruitingKpiByUserVO cloneVoForTree(RecruitingKpiByUserVO source) {
        RecruitingKpiByUserVO clone = new RecruitingKpiByUserVO();

        BeanUtils.copyProperties(source, clone);
        // 不复制 children，避免循环引用
        clone.setChildren(null);

        return clone;
    }

    /**
     * 安全地构建团队子节点，避免循环引用
     */
    private List<RecruitingKpiByUserVO> buildTeamChildrenSafe(Long parentTeamId, String date,
                                                            List<RecruitingKpiByUserVO> allVoList, String parentNodeId) {
        List<RecruitingKpiByUserVO> children = new ArrayList<>();

        for (RecruitingKpiByUserVO vo : allVoList) {
            // 找到属于该父团队的子节点（子团队或用户）
            if ((parentTeamId.equals(vo.getTeamParentId()) ||
                (parentTeamId.equals(vo.getTeamId()) && vo.getUserId() != null)) &&
                (date == null || date.equals(vo.getGroupByDate()))) {

                RecruitingKpiByUserVO child = cloneVoForTree(vo);
                child.setParentId(parentNodeId);
                child.setIsLeaf(vo.getUserId() != null);

                // 如果是子团队，递归构建其子节点
                if (vo.getUserId() == null && vo.getTeamId() != null && !vo.getTeamId().equals(parentTeamId)) {
                    String childNodeId = (date != null ? date + "-" : "") + vo.getTeamId();
                    child.setId(childNodeId);
                    child.setChildren(buildTeamChildrenSafe(vo.getTeamId(), date, allVoList, childNodeId));
                } else {
                    child.setChildren(new ArrayList<>());
                }

                children.add(child);
            }
        }

        return children;
    }



    @Override
    public List<RecruitingKpiByUserVO> drillRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        // 个人数据权限不允许下钻
        if (Boolean.TRUE.equals(teamDTO.getSelf())) {
           return List.of();
        }
        List<RecruitingKpiByUserVO> teamData = drillRecruitingKpiReportByTeam(searchDto, teamDTO);
        List<RecruitingKpiByUserVO> userData = drillRecruitingKpiReportByUserV2(searchDto, teamDTO);
        return Stream.of(teamData, userData).flatMap(List::stream).peek(RecruitingKpiCommonCountVO::assignIdAndParentId)
                .collect(Collectors.toList());
    }

    @Override
    public RecruitingKpiByUserVO searchRecruitingKpiTotalByUser(RecruitingKpiReportSearchDto searchDto) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TENANT));
        searchDto.setParentId(null);
        return searchRecruitingKpiReportByUser(searchDto, teamDTO).stream().findFirst().orElse(new RecruitingKpiByUserVO());
    }


    @Override
    public void exportRecruitingKpiReportByUserExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(searchDto.getGroupByFieldList())) {
            throw new CustomParameterizedException("groupByFieldList cannot be empty");
        }
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);

        List<RecruitingKpiGroupByFieldType> teamDim = List.of(RecruitingKpiGroupByFieldType.TEAM);
        List<RecruitingKpiGroupByFieldType> userDim = List.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER);
        Optional<RecruitingKpiGroupByFieldType> timeDimOpt = searchDto.getGroupByFieldList().stream()
                .filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findFirst();
        if (timeDimOpt.isPresent()) {
            switch (searchDto.getViewType()) {
                case TEAM -> {
                    teamDim = List.of(RecruitingKpiGroupByFieldType.TEAM, timeDimOpt.get());
                    userDim = List.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER, timeDimOpt.get());
                }
                case TIMELINE -> {
                    teamDim = List.of(timeDimOpt.get(), RecruitingKpiGroupByFieldType.TEAM);
                    userDim = List.of(timeDimOpt.get(), RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER);
                }
            }
        }
        // 查询团队数据，放在 sheet1
        RecruitingKpiReportSearchDto teamSearchDto = searchDto.deepCopy();
        teamSearchDto.setGroupByFieldList(teamDim);
        List<RecruitingKpiByUserVO> teamVoList = searchRecruitingKpiReportByUser(teamSearchDto, teamDTO);
        // 查询用户数据，放在 sheet2
        RecruitingKpiReportSearchDto userSearchDto = searchDto.deepCopy();
        userSearchDto.setGroupByFieldList(userDim);
        List<RecruitingKpiByUserVO> userVoList = searchRecruitingKpiReportByUser(userSearchDto, teamDTO);
        // 查询总计数据，放在 sheet1 和 sheet2 的最后一行
        RecruitingKpiByUserVO totalVo = searchRecruitingKpiTotalByUser(searchDto);
        totalVo.setTeamName("Total");

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDate = LocalDateTime.now(ZoneId.of(searchDto.getTimezone())).format(formatter);
        ExcelUtil.setFileDownloadHeader(response, formatDate + "-Recruiting KPI Reports-By Use.xlsx");

        List<String> removeHeaders = RecruitingKpiByUserExcelVo.removeHeaders(searchDto);

        Class<RecruitingKpiByUserExcelVo> excelClass = RecruitingKpiByUserExcelVo.class;

        // 团队 sheet 去掉 user 列
        List<String> teamRemoveHeaders = Stream.concat(Stream.of("user"), removeHeaders.stream()).toList();
        List<List<String>> teamSheetHeaders = ExcelUtil.getTableHeaders(excelClass, teamRemoveHeaders);
        List<List<String>> userSheetHeaders = ExcelUtil.getTableHeaders(excelClass, removeHeaders);

        try {
            ServletOutputStream outputStream = response.getOutputStream();
            try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).registerWriteHandler(new CustomCellWriteWeightConfig()).build()) {

                WriteSheet teamSheetWriter = EasyExcelFactory.writerSheet(0, "Team").head(teamSheetHeaders).needHead(true).build();
                List<RecruitingKpiByUserExcelVo> teamData = Stream.concat(teamVoList.stream(), Stream.of(totalVo))
                        .map(vo -> RecruitingKpiByUserExcelVo.ofSearchResult(vo, searchDto))
                        .toList();
                excelWriter.write(ExcelUtil.convertToMap(teamData, excelClass, teamRemoveHeaders), teamSheetWriter);

                WriteSheet userSheetWriter = EasyExcelFactory.writerSheet(1, "User").head(userSheetHeaders).needHead(true).build();
                List<RecruitingKpiByUserExcelVo> userData = Stream.concat(userVoList.stream(), Stream.of(totalVo))
                        .map(vo -> RecruitingKpiByUserExcelVo.ofSearchResult(vo, searchDto))
                        .toList();
                excelWriter.write(ExcelUtil.convertToMap(userData, excelClass, removeHeaders), userSheetWriter);
            } catch (Exception e) {
                log.error("[apn @{}] export recruiting kpi report by user excel error", SecurityUtils.getUserId(), e);
            }
        } catch (IOException e) {
            log.error("[apn @{}] export recruiting kpi report by user excel IO exception", SecurityUtils.getUserId(), e);
        }
    }

    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUser(RecruitingKpiReportSearchDto searchDto, TeamDataPermissionRespDTO teamDTO) {
        log.info("[apn @{}] search recruiting kpi report by user, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by user");

        stopWatch.start("[2] search job、talent、application、note、company info、crm company info data task");
        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());
        boolean skipQuery = searchDto.isXxlJobFlag() || searchDto.isE5ReportFlag();

        var applicationFuture = CompletableFuture.supplyAsync(() -> recruitingKpiUserRepositoryV2.searchApplicationKpiData(searchDto, teamDTO)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);

        var reserveInterviewFuture = CompletableFuture.supplyAsync(() -> {
            if (RecruitingKpiDateType.ADD.equals(searchDto.getDateType())) {
                return Collections.<String, ReserveInterviewVO>emptyMap();
            }
            return recruitingKpiUserRepositoryV2.searchReserveInterviewKpiData(searchDto, teamDTO)
                    .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var noteFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiNoteCountVO> voList = searchDto.isXxlJobFlag() ? List.of() : recruitingKpiUserRepositoryV2.searchNoteKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var createdDataFuture = CompletableFuture.supplyAsync(() -> {
            List<KpiReportCreatedVO> voList = skipQuery ? List.of() : recruitingKpiUserRepositoryV2.searchCreatedKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        var jobOpeningsFuture = CompletableFuture.supplyAsync(() -> {
            List<RecruitingKpiCommonCountVO> voList = skipQuery ? List.of() : recruitingKpiUserRepositoryV2.searchJobOpeningsKpiData(searchDto, teamDTO);
            return voList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        }, starRocksExecutor);

        CompletableFuture.allOf(applicationFuture, noteFuture, createdDataFuture, jobOpeningsFuture).exceptionally(FutureExceptionUtil::handleFutureException);

        var applicationMap = applicationFuture.join();
        var reserveInterviewMap = reserveInterviewFuture.join();
        var noteMap = noteFuture.join();
        var createdDataMap = createdDataFuture.join();
        var jobMap = jobOpeningsFuture.join();
        stopWatch.stop();

        Set<String> keySet = Stream.of(applicationMap, reserveInterviewMap, noteMap, createdDataMap, jobMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        stopWatch.start("[4] assemble data task");

        CopyOnWriteArrayList<RecruitingKpiByUserVO> voList = new CopyOnWriteArrayList<>();
        keySet.parallelStream().forEach(key -> {
            try {
                RecruitingKpiByUserVO vo = new RecruitingKpiByUserVO();
                setGroupByField(vo, key, searchDto.getGroupByFieldList());
                setVoNumV2(vo, applicationMap, reserveInterviewMap, noteMap, createdDataMap, jobMap, key, searchDto);
                voList.add(vo);
            } catch (Exception e) {
                log.error(" search kpi by user assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                throw e;
            }
        });
        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByUser time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return voList;
    }

    @Override
    public List<RecruitingKpiByUserVO> searchRecruitingKpiReportByUserForE5(RecruitingKpiReportSearchDto searchDto) {
        return List.of();
    }


    @Override
    public List<RecruitingKpiByCompanyVO> searchRecruitingKpiReportByCompany(RecruitingKpiReportSearchDto searchDto) {

        log.info("[apn @{}] search recruiting kpi report by company v2, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("recruiting kpi report by company v2");
        stopWatch.start("[1] search company permission data task");
        //init report permission
        getPermissionDTOAndSetCommonParam(searchDto);
        // if group by company, ignore user active status filter
        stopWatch.stop();
        stopWatch.start("[2] search company data task");

        Function<RecruitingKpiCommonCountVO, String> function = getCompanyMapKey(searchDto.getGroupByFieldList());

        //判断是否存在job维护
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);

        var applicationFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchApplicationKpiData(searchDto)
                .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);

        var applicationMap = applicationFuture.join();

        // 获取查询结果keySet
        Set<String> keySet = Stream.of(applicationMap)
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        // 公司id，查询本周和上周推荐至客户和公司进度备注数
        List<RecruitingKpiByCompanyVO> companyVoList;

        List<Long> companyIdList;

        //job视图 查询job总数 要根据CompanyID查询
        if (null != searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
            companyIdList = new ArrayList<>(applicationMap.values().stream().map(RecruitingKpiApplicationCountVO::getCompanyId)
                    .filter(Objects::nonNull)
                    .toList());
            if (CollUtil.isNotEmpty(searchDto.getCompanyIdList())) {
                companyIdList.addAll(searchDto.getCompanyIdList());
            }
            if (companyIdList.isEmpty()) {
                companyIdList.add(-1L);
            }
            //赋值CompanyId
            SearchKpiCompanyDto companyDto = searchDto.getCompany();
            if (null == companyDto) {
                companyDto = new SearchKpiCompanyDto();
            }
            companyDto.setIdList(companyIdList);
            searchDto.setCompany(companyDto);
        }

        //查询job
        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchJobKpiByCompany(searchDto, jobDetailFlag), starRocksExecutor);
        List<? extends RecruitingKpiCommonCountVO> jobList = jobFuture.join();
        ConcurrentMap<String, RecruitingKpiJobDetailCommonCountVO> jobDetailMap = !jobDetailFlag ? new ConcurrentHashMap<>() : jobList.stream().collect(Collectors.toConcurrentMap(function, a -> (RecruitingKpiJobDetailCommonCountVO) a));
        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap = jobList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        if (BooleanUtil.isTrue(jobDetailFlag)) {
            keySet.addAll(jobDetailMap.keySet());
        } else {
            keySet.addAll(jobMap.keySet());
        }

        //查询job note
        CompletableFuture<List<RecruitingKpiCommonCountVO>> jobNoteFuture = CompletableFuture.supplyAsync(() -> {
            if (searchDto.isXxlJobFlag() || !searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB)) {
                return new ArrayList<>();
            }
            return recruitingKpiCompanyRepositoryV2.searchJobNoteKpi(searchDto, jobDetailFlag);
        }, starRocksExecutor);

        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobNoteMap = jobNoteFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        keySet.addAll(jobNoteMap.keySet());

        //获取所有companyIdList
        Pair<List<RecruitingKpiByCompanyVO>, List<Long>> companyPair = saveCompanyId(keySet, searchDto);
        companyVoList = companyPair.getKey();
        companyIdList = companyPair.getValue().stream().distinct().toList();
        //重新赋值 将jobMap和jobNoteMap也加进来
        SearchKpiCompanyDto companyDto = new SearchKpiCompanyDto();
        companyDto.setIdList(companyIdList);
        searchDto.setCompany(companyDto);

        stopWatch.stop();
        stopWatch.start("[3] assemble data task");

        //查询上周和本周提交至客户数据
        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>>> twoWeekMapFuture = recruitingKpiCompanyRepositoryV2.getSubmitToClientWithWeek(searchDto, false);
        ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>> twoWeekMap = twoWeekMapFuture.join();

        //补全公司数据
        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanyInfoVO>>> companyListMapFuture = recruitingKpiCompanyRepositoryV2.getCompanyInfo(companyIdList, searchDto);
        ConcurrentMap<Long, List<KpiReportCompanyInfoVO>> companyIdInfoMap = companyListMapFuture.join();

        //查询业务进展备注
        CompletableFuture<ConcurrentMap<Long, Long>> progressNoteMapFuture = getBdReportProgressNoteStats(companyIdList, searchDto);
        ConcurrentMap<Long, Long> companyIdCountMap = progressNoteMapFuture.join();

        //返回结果
        CopyOnWriteArrayList<RecruitingKpiByCompanyVO> voList = new CopyOnWriteArrayList<>();
        if (BooleanUtil.isTrue(jobDetailFlag)) {
            ConcurrentMap<Long, RecruitingKpiJobDetailCommonCountVO> jobDetailMapByJobId = jobList.stream().collect(Collectors.toConcurrentMap(RecruitingKpiCommonCountVO::getJobId, a -> (RecruitingKpiJobDetailCommonCountVO) a));
            // job detail 的情况处理
            companyVoList.parallelStream().forEach(vo -> {
                try {
                    // 分组肯定有jobId
                    RecruitingKpiJobDetailCommonCountVO jobDetailVO = jobDetailMapByJobId.get(vo.getJobId());
                    vo.setNum(vo, applicationMap, jobMap, vo.getKey(), searchDto);
                    // 调用 AI 推荐相关设置
                    setAIRecommendV2(vo, searchDto, applicationMap.getOrDefault(vo.getKey(), new RecruitingKpiApplicationCountVO()), new ReserveInterviewVO(), vo.getKey());
                    if (ObjectUtil.isNotNull(jobDetailVO)) {
                        vo.setContacts(jobDetailVO.getContacts());
                        vo.setAssignedUser(jobDetailVO.getAssignedUser());
                        vo.setJobStatus(jobDetailVO.getJobStatus());
                        vo.setJobStartDate(jobDetailVO.getJobStartDate());
                        vo.setJobEndDate(jobDetailVO.getJobEndDate());
                        vo.setContractDuration(jobDetailVO.getContractDuration());
                        vo.setJobCurrency(jobDetailVO.getJobCurrency());
                        vo.setMinimumPayRate(jobDetailVO.getMinimumPayRate());
                        vo.setMaximumPayRate(jobDetailVO.getMaximumPayRate());
                        vo.setOpenings(jobDetailVO.getCountNum());
                        vo.setJobIds(jobDetailVO.getJobId() + "");
                        vo.setJobCooperationStatus(jobDetailVO.getJobCooperationStatus());
                        vo.setPrivateJob(jobDetailVO.isPrivateJob());
                    }
                    vo.setJobNoteNum(jobNoteMap.getOrDefault(vo.getKey(), new RecruitingKpiCommonCountVO()).getCountNum());
                    vo.setBdReportProgressNoteCount(companyIdCountMap.get(vo.getCompanyId()));
                    setCompanyInfo(vo, companyIdInfoMap, twoWeekMap, vo.getJobId());
                    if (null != searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
                        if (!jobDetailFlag) {
                            vo.setIsLeaf(false);
                        } else {
                            vo.setIsLeaf(true);
                        }
                        vo.setParentId(vo.getCompanyId() + "");
                    } else {
                        vo.setIsLeaf(true);
                    }
                    voList.add(vo);
                } catch (Exception e) {
                    log.error(" search kpi by client assemble data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                    throw e;
                }
            });
        } else {
            companyVoList.parallelStream().forEach(vo -> {
                try {
                    vo.setNum(vo, applicationMap, jobMap, vo.getKey(), searchDto);
                    // 调用 AI 推荐相关设置
                    setAIRecommendV2(vo, searchDto, applicationMap.getOrDefault(vo.getKey(), new RecruitingKpiApplicationCountVO()), new ReserveInterviewVO(), vo.getKey());
                    vo.setJobNoteNum(jobNoteMap.getOrDefault(vo.getKey(), new RecruitingKpiTalentNoteCountVO()).getCountNum());
                    if (!searchDto.isXxlJobFlag()) {
                        vo.setBdReportProgressNoteCount(companyIdCountMap.get(vo.getCompanyId()));
                        setCompanyInfo(vo, companyIdInfoMap, twoWeekMap, null);
                    }
                    if (null != searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
                        if (!jobDetailFlag) {
                            vo.setIsLeaf(false);
                        } else {
                            vo.setIsLeaf(true);
                        }
                        vo.setParentId(vo.getCompanyId() + "");
                    } else {
                        vo.setIsLeaf(true);
                    }
                    voList.add(vo);
                } catch (Exception e) {
                    log.error(" search kpi assemble by client data is error, msg = {}", ExceptionUtil.getAllExceptionMsg(e));
                    throw e;
                }
            });
        }

        stopWatch.stop();
        log.info("[apn @{}] searchRecruitingKpiReportByCompany time = {}ms \n {}", searchDto.getSearchUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        return sortVoListByFields(voList, searchDto.getGroupByFieldList());
    }

    /**
     * 查询E2统计
     *
     * @param searchDto
     * @return
     */
    @Override
    public RecruitingKpiByCompanyVO searchRecruitingKpiTotalByCompany(RecruitingKpiReportSearchDto searchDto) {
        getPermissionDTOAndSetCommonParam(searchDto);

        Function<RecruitingKpiCommonCountVO, String> function = getMapKey(searchDto.getGroupByFieldList());

        //查询流程数据
        RecruitingKpiReportSearchDto copySearchDto = searchDto.deepCopy();
        copySearchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TENANT));
        copySearchDto.setParentId(null);
        Function<RecruitingKpiCommonCountVO, String> applicationFunction = getMapKey(copySearchDto.getGroupByFieldList());
        var applicationFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchApplicationKpiData(copySearchDto)
                .parallelStream().collect(Collectors.toConcurrentMap(applicationFunction, a -> a)), starRocksExecutor);
        var applicationMap = applicationFuture.join();

        List<Long> companyIdList = new ArrayList<>();

        //如果前端给了CompanyID 可以直接查询补全Company数据 否则查询
        if (null != searchDto.getCompany() && CollUtil.isNotEmpty(searchDto.getCompany().getIdList())) {
            companyIdList.addAll(searchDto.getCompany().getIdList());
        } else {
            //查询流程中的公司id
            var applicationCompanyFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchApplicationKpiCompanyData(searchDto)
                    .parallelStream().collect(Collectors.toConcurrentMap(function, a -> a)), starRocksExecutor);
            var applicationCompanyMap = applicationCompanyFuture.join();
            List<Long> applictionCompanyIdList = Stream.of(applicationCompanyMap).parallel()
                    .flatMap(map -> map.values().stream()
                            .map(x -> x.getCompanyId())).distinct().toList();
            companyIdList.addAll(applictionCompanyIdList);
        }


        //job视图 查询job总数 要根据CompanyID查询
        if (null != searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
            if (CollUtil.isNotEmpty(searchDto.getCompanyIdList())) {
                companyIdList.addAll(searchDto.getCompanyIdList());
            }
            SearchKpiCompanyDto companyDto = searchDto.getCompany();
            if (null == companyDto) {
                companyDto = new SearchKpiCompanyDto();
            }
            if(companyIdList.isEmpty()){
                companyIdList.add(-1L);
            }
            companyDto.setIdList(companyIdList);
            searchDto.setCompany(companyDto);
        }

        //判断是否存在job维护
        boolean jobDetailFlag = searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB);

        CompletableFuture<List<? extends RecruitingKpiCommonCountVO>> jobFuture = CompletableFuture.supplyAsync(() -> recruitingKpiCompanyRepositoryV2.searchJobKpiByCompany(searchDto, false), starRocksExecutor);
        List<? extends RecruitingKpiCommonCountVO> jobList = jobFuture.join();
        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap = jobList.parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));
        companyIdList.addAll(getCompanyIdByJobNote(jobMap));

        //job note
        CompletableFuture<List<RecruitingKpiCommonCountVO>> jobNoteFuture = CompletableFuture.supplyAsync(() -> {
            if (!jobDetailFlag) {
                return new ArrayList<>();
            }
            return recruitingKpiCompanyRepositoryV2.searchJobNoteKpi(searchDto, true);
        }, starRocksExecutor);

        ConcurrentMap<String, RecruitingKpiCommonCountVO> jobNoteMap = jobNoteFuture.join().parallelStream().collect(Collectors.toConcurrentMap(function, a -> a));


        //补全公司数据 获取公司备注信息
        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanyInfoVO>>> companyListMapFuture = recruitingKpiCompanyRepositoryV2.getCompanyInfo(companyIdList, searchDto);
        ConcurrentMap<Long, List<KpiReportCompanyInfoVO>> companyIdInfoMap = companyListMapFuture.join();

        //查询业务进展备注
        CompletableFuture<ConcurrentMap<Long, Long>> progressNoteMapFuture = getBdReportProgressNoteStats(companyIdList, searchDto);
        ConcurrentMap<Long, Long> companyIdCountMap = progressNoteMapFuture.join();

        //查询上周和本周提交至客户数据 维度为Companyid
        CompletableFuture<ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>>> twoWeekMapFuture = recruitingKpiCompanyRepositoryV2.getSubmitToClientWithWeek(searchDto, true);
        ConcurrentMap<Long, List<KpiReportCompanySubmitToClientTwoWeekVO>> twoWeekMap = twoWeekMapFuture.join();

        Set<String> applicationKeySet = Stream.of(applicationMap).parallel()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

        //组装数据返回结果
        RecruitingKpiByCompanyVO vo = new RecruitingKpiByCompanyVO();
        if(!applicationKeySet.isEmpty()){
            vo.setNum(vo, applicationMap, new ConcurrentHashMap<>(), applicationKeySet.stream().findFirst().get(), searchDto);
            //ai相关信息
            setAIRecommendV2(vo, searchDto, applicationMap.getOrDefault(applicationKeySet.stream().findFirst().get(), new RecruitingKpiApplicationCountVO()), new ReserveInterviewVO(), applicationKeySet.stream().findFirst().get());
        }

        //获取职位总数
        AtomicReference<Long> jobOpeningCount = new AtomicReference<>(0L);
        jobMap.forEach((k, v) -> {
            if (v != null && null != v.getCountNum()) {
                jobOpeningCount.updateAndGet(v1 -> v1 + v.getCountNum());
            }
        });

        AtomicReference<Long> companyNoteCount = new AtomicReference<>(0L);
        companyIdInfoMap.forEach((k, v) -> {
            if (v != null && !v.isEmpty()) {
                // 获取 List 的第一个元素
                KpiReportCompanyInfoVO firstItem = v.get(0);
                if (firstItem != null && firstItem.getCompanyNoteCount() != null) {
                    companyNoteCount.updateAndGet(v1 -> v1 + firstItem.getCompanyNoteCount());
                }
            }
        });

        // 设置基础字段
        vo.setOpenings(jobOpeningCount.get());
        vo.setBdReportProgressNoteCount(companyIdCountMap.values().stream().reduce(0L, Long::sum));
        vo.setJobNoteNum(jobNoteMap.isEmpty() ? 0L : jobNoteMap.values().stream().map(RecruitingKpiCommonCountVO::getCountNum).reduce(0L, Long::sum));
        vo.setCompanyNoteCount(companyNoteCount.get());

        //本周和上周推进至客户数据
        if (!twoWeekMap.isEmpty()) {
            setThisWeekAndLastWeek(vo, twoWeekMap.get(SecurityUtils.getTenantId()));
            setAiThisWeekAndLastWeekRecommend(vo, twoWeekMap.get(SecurityUtils.getTenantId()));
        }
        return vo;
    }

    @Override
    public void exportRecruitingKpiReportByCompanyExcel(RecruitingKpiReportSearchDto searchDto, HttpServletResponse response) {

        if (null != searchDto.getViewType() && RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
            searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.COMPANY, RecruitingKpiGroupByFieldType.JOB));
        }

        //查询主体数据
        List<RecruitingKpiByCompanyVO> bodyList = searchRecruitingKpiReportByCompany(searchDto);

        //查询统计
        RecruitingKpiByCompanyVO total = searchRecruitingKpiTotalByCompany(searchDto);
        total.setCompanyName("Total");

        //查询币种，导出转换使用
        List<EnumCurrency> currencyList = commonService.findAllEnumCurrency();
        Map<Integer, EnumCurrency> currencyMap = currencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));

        List<EnumIndustry> industrieList = commonService.findAllEnumIndustry();
        Map<Long, EnumIndustry> industryMap = industrieList.stream().collect(Collectors.toMap(EnumIndustry::getId, a -> a));

        bodyList.forEach(value -> {
            if (StringUtils.isNotBlank(value.getJobCurrency()) && currencyMap.containsKey(Integer.valueOf(value.getJobCurrency()))) {
                EnumCurrency currency = currencyMap.get(Integer.valueOf(value.getJobCurrency()));
                value.setJobCurrency(currency.getName() + "/" + currency.getLabel1());
            }
            if (StringUtils.isNotBlank(value.getIndustries())) {
                String[] industriesStr = value.getIndustries().split(",");
                StringBuilder industries = new StringBuilder();
                for (String industryStr : industriesStr) {
                    if (industryMap.containsKey(Long.valueOf(industryStr))) {
                        industries.append(industryMap.get(Long.valueOf(industryStr)).getName()).append(",");
                    }
                }
                value.setIndustries(industries.toString());
            }
        });


        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formatDate = LocalDateTime.now(ZoneId.of(searchDto.getTimezone())).format(formatter);
        ExcelUtil.setFileDownloadHeader(response, formatDate + "-Recruiting KPI Reports-By Client Export.xlsx");

        List<String> removeHeaders = RecruitingKpiByCompanyExcelVo.removeHeaders(searchDto);
        if (null != searchDto.getViewType()) {
            //不是Job视图去掉job相关内容
            if (!RecruitingKpiViewType.JOB.equals(searchDto.getViewType())) {
                List<String> jobHeaders = List.of("jobStatus", "contactDuration", "startDate", "endDate", "contacts", "jobNotes", "jobCurrency", "jobDesiredSalaryRange", "jobCooperationStatus");
                removeHeaders.addAll(jobHeaders);
            } else {
                List<String> jobHeaders = List.of("team", "user");
                removeHeaders.addAll(jobHeaders);
            }
        } else {
            if (!searchDto.getGroupByFieldList().contains(RecruitingKpiGroupByFieldType.JOB)) {
                List<String> jobHeaders = List.of("jobStatus", "contactDuration", "startDate", "endDate", "contacts", "jobNotes", "jobCurrency", "jobDesiredSalaryRange", "jobCooperationStatus");
                removeHeaders.addAll(jobHeaders);
            } else {
                List<String> jobHeaders = List.of("team", "user");
                removeHeaders.addAll(jobHeaders);
            }
        }

        Class<RecruitingKpiByCompanyExcelVo> excelClass = RecruitingKpiByCompanyExcelVo.class;

        List<List<String>> companyJobHeaders = ExcelUtil.getTableHeaders(excelClass, removeHeaders);

        try {
            ServletOutputStream outputStream = response.getOutputStream();
            try (ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).registerWriteHandler(new CustomCellWriteWeightConfig()).build()) {

                WriteSheet sheetWriter = EasyExcelFactory.writerSheet(0, "company-sheet").head(companyJobHeaders).needHead(true).build();
                List<RecruitingKpiByCompanyExcelVo> companyData = Stream.concat(bodyList.stream(), Stream.of(total))
                        .map(vo -> RecruitingKpiByCompanyExcelVo.ofSearchResult(vo, searchDto))
                        .toList();
                excelWriter.write(ExcelUtil.convertToMap(companyData, excelClass, removeHeaders), sheetWriter);
            } catch (Exception e) {
                log.error("[apn @{}] export recruiting kpi report by company excel error", SecurityUtils.getUserId(), e);
            }
        } catch (IOException e) {
            log.error("[apn @{}] export recruiting kpi report by user company IO exception", SecurityUtils.getUserId(), e);
        }

    }
}
