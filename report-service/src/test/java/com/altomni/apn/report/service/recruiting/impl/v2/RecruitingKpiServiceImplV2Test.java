package com.altomni.apn.report.service.recruiting.impl.v2;

import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiViewType;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.List;

import static org.junit.Assert.*;

public class RecruitingKpiServiceImplV2Test {

    private RecruitingKpiByUserTreeHelper recruitingKpiService = new RecruitingKpiByUserTreeHelper();

    private ObjectMapper objectMapper;
    private List<RecruitingKpiByUserVO> testData;

    @Before
    public void setUp() throws IOException {
        objectMapper = new ObjectMapper();
        
        // 加载测试数据
        ClassPathResource resource = new ClassPathResource("report.json");
        testData = objectMapper.readValue(resource.getInputStream(), new TypeReference<List<RecruitingKpiByUserVO>>() {});
    }

    @Test
    public void testBuildKpiTreeStructure_TeamWithTime() {
        // 准备测试数据
        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
        searchDto.setViewType(RecruitingKpiViewType.TEAM);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.DAY));

        // 执行测试
        List<RecruitingKpiByUserVO> result = recruitingKpiService.buildKpiTreeStructure(testData, searchDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证根节点都是团队节点
        for (RecruitingKpiByUserVO rootNode : result) {
            assertNotNull(rootNode.getTeamId());
            assertNull(rootNode.getUserId());
            assertFalse(rootNode.getIsLeaf());
            assertNotNull(rootNode.getChildren());
            
            // 验证第二级是时间节点
            for (RecruitingKpiByUserVO timeNode : rootNode.getChildren()) {
                assertNotNull(timeNode.getGroupByDate());
                assertFalse(timeNode.getIsLeaf());
                assertEquals(rootNode.getId(), timeNode.getParentId());
            }
        }
        
        System.out.println("TEAM + TIME 树结构根节点数量: " + result.size());
        printTreeStructure(result, 0);
    }

    @Test
    public void testBuildKpiTreeStructure_TeamOnly() {
        // 准备测试数据
        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
        searchDto.setViewType(RecruitingKpiViewType.TEAM);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM));

        // 执行测试
        List<RecruitingKpiByUserVO> result = recruitingKpiService.buildKpiTreeStructure(testData, searchDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证根节点都是团队节点
        for (RecruitingKpiByUserVO rootNode : result) {
            assertNotNull(rootNode.getTeamId());
            assertNull(rootNode.getUserId());
            assertFalse(rootNode.getIsLeaf());
            assertNotNull(rootNode.getChildren());
        }
        
        System.out.println("TEAM ONLY 树结构根节点数量: " + result.size());
        printTreeStructure(result, 0);
    }

    @Test
    public void testBuildKpiTreeStructure_Timeline() {
        // 准备测试数据
        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
        searchDto.setViewType(RecruitingKpiViewType.TIMELINE);
        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.DAY, RecruitingKpiGroupByFieldType.TEAM));

        // 执行测试
        List<RecruitingKpiByUserVO> result = recruitingKpiService.buildKpiTreeStructure(testData, searchDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证根节点都是时间节点
        for (RecruitingKpiByUserVO rootNode : result) {
            assertNotNull(rootNode.getGroupByDate());
            assertFalse(rootNode.getIsLeaf());
            assertNotNull(rootNode.getChildren());
            
            // 验证第二级是团队节点
            for (RecruitingKpiByUserVO teamNode : rootNode.getChildren()) {
                assertNotNull(teamNode.getTeamId());
                assertFalse(teamNode.getIsLeaf());
                assertEquals(rootNode.getId(), teamNode.getParentId());
            }
        }
        
        // 验证时间节点是按时间排序的
        for (int i = 1; i < result.size(); i++) {
            String prevDate = result.get(i - 1).getGroupByDate();
            String currDate = result.get(i).getGroupByDate();
            assertTrue("时间节点应该按时间排序",prevDate.compareTo(currDate) <= 0);
        }
        
        System.out.println("TIMELINE 树结构根节点数量: " + result.size());
        printTreeStructure(result, 0);
    }

    /**
     * 打印树结构，用于调试
     */
    private void printTreeStructure(List<RecruitingKpiByUserVO> nodes, int level) {
        String indent = "  ".repeat(level);
        for (RecruitingKpiByUserVO node : nodes) {
            String nodeInfo = String.format("%s- %s (ID: %s, TeamId: %s, UserId: %s, Date: %s, IsLeaf: %s)", 
                indent, 
                node.getTeamName() != null ? node.getTeamName() : node.getUserName() != null ? node.getUserName() : "TimeNode",
                node.getId(),
                node.getTeamId(),
                node.getUserId(),
                node.getGroupByDate(),
                node.getIsLeaf()
            );
            System.out.println(nodeInfo);
            
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                printTreeStructure(node.getChildren(), level + 1);
            }
        }
    }
}
