[{"id": "2025-07-14-111", "parentId": "2025-07-14-103", "teamId": 111, "teamName": "China-test team", "teamLevel": 2, "isLeaf": false, "teamParentId": 103, "groupByDate": "2025-07-14", "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0}, {"id": "2025-07-14-103", "parentId": "2025-07-14-102", "teamId": 103, "teamName": "wuhan", "teamLevel": 1, "isLeaf": false, "teamParentId": 102, "groupByDate": "2025-07-14", "talentNum": 8, "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 4, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 0, "apnProNoteNum": 0, "noteCount": 4}, {"id": "2025-07-14-102", "parentId": "2025-07-14--1", "teamId": 102, "teamName": "IPG", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-14", "openings": 10, "talentNum": 15, "submitToJobNum": 5, "submitToClientNum": 4, "firstInterviewNum": 3, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 3, "interviewNumProcess": 3, "interviewAppointments": 3, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 2, "offerNum": 3, "offerAcceptNum": 3, "onboardNum": 3, "eliminateNum": 0, "stayedOver24Hrs": 1, "stayedOver72Hrs": 1, "submitToJobNumAIRecommend": 2, "submitToClientNumAIRecommend": 2, "firstInterviewNumAIRecommend": 2, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 2, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 2, "offerNumAIRecommend": 2, "offerAcceptNumAIRecommend": 2, "onboardNumAIRecommend": 2, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 2, "callNoteNum": 5, "emailNoteNum": 0, "personNoteNum": 1, "videoNoteNum": 0, "otherNoteNum": 2, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 1, "apnProNoteNum": 0, "noteCount": 8}, {"id": "2025-06-30-103", "parentId": "2025-06-30-102", "teamId": 103, "teamName": "wuhan", "teamLevel": 1, "isLeaf": false, "teamParentId": 102, "groupByDate": "2025-06-30", "submitToJobNum": 2, "submitToClientNum": 2, "firstInterviewNum": 2, "secondInterviewNum": 1, "finalInterviewNum": 0, "interviewNum": 3, "interviewNumProcess": 2, "interviewAppointments": 3, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 1, "offerNum": 0, "offerAcceptNum": 0, "onboardNum": 0, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0}, {"id": "2025-07-07-131", "parentId": "2025-07-07-111", "teamId": 131, "teamName": "china_test", "teamLevel": 3, "isLeaf": false, "teamParentId": 111, "groupByDate": "2025-07-07", "openings": 1, "talentNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-21-102", "parentId": "2025-07-21--1", "teamId": 102, "teamName": "IPG", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-21", "openings": 40, "talentNum": 3, "submitToJobNum": 0, "submitToClientNum": 1, "firstInterviewNum": 0, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 0, "interviewNumProcess": 0, "interviewAppointments": 0, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 0, "offerNum": 0, "offerAcceptNum": 0, "onboardNum": 0, "eliminateNum": 0, "stayedOver72Hrs": 1, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 2, "emailNoteNum": 1, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 0, "apnProNoteNum": 0, "noteCount": 3}, {"id": "2025-07-21-103", "parentId": "2025-07-21-102", "teamId": 103, "teamName": "wuhan", "teamLevel": 1, "isLeaf": false, "teamParentId": 102, "groupByDate": "2025-07-21", "callNoteNum": 2, "emailNoteNum": 1, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "pipelineNoteNum": 0, "apnProNoteNum": 0, "noteCount": 3}, {"id": "2025-06-30-102", "parentId": "2025-06-30--1", "teamId": 102, "teamName": "IPG", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-06-30", "openings": 1, "talentNum": 2, "submitToJobNum": 6, "submitToClientNum": 6, "firstInterviewNum": 5, "secondInterviewNum": 1, "finalInterviewNum": 0, "interviewNum": 6, "interviewNumProcess": 5, "interviewAppointments": 6, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 4, "offerNum": 2, "offerAcceptNum": 2, "onboardNum": 2, "eliminateNum": 0, "stayedOver72Hrs": 1, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 0, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 1, "createdCompaniesNum": 2, "pipelineNoteNum": 13, "apnProNoteNum": 0, "noteCount": 0}, {"id": "2025-06-30-1240", "parentId": "2025-06-30--1", "teamId": 1240, "teamName": "419", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-06-30", "openings": 21, "talentNum": 0, "submitToJobNum": 5, "submitToClientNum": 5, "firstInterviewNum": 4, "secondInterviewNum": 1, "finalInterviewNum": 0, "interviewNum": 5, "interviewNumProcess": 4, "interviewAppointments": 5, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 3, "offerNum": 2, "offerAcceptNum": 2, "onboardNum": 2, "eliminateNum": 0, "stayedOver72Hrs": 1, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 1, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 7, "apnProNoteNum": 0, "noteCount": 1}, {"id": "2025-07-28-1240", "parentId": "2025-07-28--1", "teamId": 1240, "teamName": "419", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-28", "talentNum": 1, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-28-102", "parentId": "2025-07-28--1", "teamId": 102, "teamName": "IPG", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-28", "openings": 1, "talentNum": 1, "submitToJobNum": 0, "submitToClientNum": 0, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 0, "offerAcceptNum": 0, "onboardNum": 0, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 2, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 1, "apnProNoteNum": 0, "noteCount": 2}, {"id": "2025-07-07-111", "parentId": "2025-07-07-103", "teamId": 111, "teamName": "China-test team", "teamLevel": 2, "isLeaf": false, "teamParentId": 103, "groupByDate": "2025-07-07", "openings": 1, "talentNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-14-131", "parentId": "2025-07-14-111", "teamId": 131, "teamName": "china_test", "teamLevel": 3, "isLeaf": false, "teamParentId": 111, "groupByDate": "2025-07-14", "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0}, {"id": "2025-07-07-1240", "parentId": "2025-07-07--1", "teamId": 1240, "teamName": "419", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-07", "openings": 33, "talentNum": 0, "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 2, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 6, "apnProNoteNum": 0, "noteCount": 2}, {"id": "2025-07-28-103", "parentId": "2025-07-28-102", "teamId": 103, "teamName": "wuhan", "teamLevel": 1, "isLeaf": false, "teamParentId": 102, "groupByDate": "2025-07-28", "talentNum": 1, "callNoteNum": 2, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 0, "apnProNoteNum": 0, "noteCount": 2}, {"id": "2025-07-14-1240", "parentId": "2025-07-14--1", "teamId": 1240, "teamName": "419", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-14", "openings": 21, "talentNum": 3, "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 1, "finalInterviewNum": 1, "interviewNum": 3, "interviewNumProcess": 1, "interviewAppointments": 3, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 1, "submitToJobNumAIRecommend": 1, "submitToClientNumAIRecommend": 1, "firstInterviewNumAIRecommend": 1, "secondInterviewNumAIRecommend": 1, "finalInterviewNumAIRecommend": 1, "interviewNumAIRecommend": 3, "twoOrMoreInterviewsAIRecommend": 1, "interviewAppointmentsAIRecommend": 3, "offerNumAIRecommend": 1, "offerAcceptNumAIRecommend": 1, "onboardNumAIRecommend": 1, "eliminateNumAIRecommend": 1, "submitToJobNumPrecisionAIRecommend": 1, "submitToClientNumPrecisionAIRecommend": 1, "firstInterviewNumPrecisionAIRecommend": 1, "secondInterviewNumPrecisionAIRecommend": 1, "finalInterviewNumPrecisionAIRecommend": 1, "interviewNumPrecisionAIRecommend": 3, "twoOrMoreInterviewsPrecisionAIRecommend": 1, "interviewAppointmentsPrecisionAIRecommend": 3, "offerNumPrecisionAIRecommend": 1, "offerAcceptNumPrecisionAIRecommend": 1, "onboardNumPrecisionAIRecommend": 1, "eliminateNumPrecisionAIRecommend": 1, "interviewNumProcessPrecisionAIRecommend": 1, "interviewNumProcessAIRecommend": 1, "callNoteNum": 0, "emailNoteNum": 1, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 2, "createdCompaniesNum": 2, "pipelineNoteNum": 8, "apnProNoteNum": 0, "noteCount": 1}, {"id": "2025-07-07-103", "parentId": "2025-07-07-102", "teamId": 103, "teamName": "wuhan", "teamLevel": 1, "isLeaf": false, "teamParentId": 102, "groupByDate": "2025-07-07", "openings": 1, "talentNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-07-102", "parentId": "2025-07-07--1", "teamId": 102, "teamName": "IPG", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-07", "openings": 1, "talentNum": 1, "submitToJobNum": 5, "submitToClientNum": 2, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 2, "offerAcceptNum": 2, "onboardNum": 2, "eliminateNum": 0, "stayedOver24Hrs": 1, "submitToJobNumAIRecommend": 1, "submitToClientNumAIRecommend": 1, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 1, "offerAcceptNumAIRecommend": 1, "onboardNumAIRecommend": 1, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 0, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 13, "apnProNoteNum": 0, "noteCount": 0}, {"id": "2025-06-30-131", "parentId": "2025-06-30-111", "teamId": 131, "teamName": "china_test", "teamLevel": 3, "isLeaf": false, "teamParentId": 111, "groupByDate": "2025-06-30"}, {"id": "2025-06-30-1147", "parentId": "2025-06-30--1", "teamId": 1147, "teamName": "ipg_test", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-06-30"}, {"id": "2025-06-30-111", "parentId": "2025-06-30-103", "teamId": 111, "teamName": "China-test team", "teamLevel": 2, "isLeaf": false, "teamParentId": 103, "groupByDate": "2025-06-30"}, {"id": "2025-06-30-1151", "parentId": "2025-06-30-1147", "teamId": 1151, "teamName": "ipg_test1", "teamLevel": 1, "isLeaf": false, "teamParentId": 1147, "groupByDate": "2025-06-30"}, {"id": "2025-07-07-1147", "parentId": "2025-07-07--1", "teamId": 1147, "teamName": "ipg_test", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-07"}, {"id": "2025-07-07-1151", "parentId": "2025-07-07-1147", "teamId": 1151, "teamName": "ipg_test1", "teamLevel": 1, "isLeaf": false, "teamParentId": 1147, "groupByDate": "2025-07-07"}, {"id": "2025-07-14-1147", "parentId": "2025-07-14--1", "teamId": 1147, "teamName": "ipg_test", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-14"}, {"id": "2025-07-14-1151", "parentId": "2025-07-14-1147", "teamId": 1151, "teamName": "ipg_test1", "teamLevel": 1, "isLeaf": false, "teamParentId": 1147, "groupByDate": "2025-07-14"}, {"id": "2025-07-21-131", "parentId": "2025-07-21-111", "teamId": 131, "teamName": "china_test", "teamLevel": 3, "isLeaf": false, "teamParentId": 111, "groupByDate": "2025-07-21"}, {"id": "2025-07-21-1240", "parentId": "2025-07-21--1", "teamId": 1240, "teamName": "419", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-21"}, {"id": "2025-07-21-1147", "parentId": "2025-07-21--1", "teamId": 1147, "teamName": "ipg_test", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-21"}, {"id": "2025-07-21-111", "parentId": "2025-07-21-103", "teamId": 111, "teamName": "China-test team", "teamLevel": 2, "isLeaf": false, "teamParentId": 103, "groupByDate": "2025-07-21"}, {"id": "2025-07-21-1151", "parentId": "2025-07-21-1147", "teamId": 1151, "teamName": "ipg_test1", "teamLevel": 1, "isLeaf": false, "teamParentId": 1147, "groupByDate": "2025-07-21"}, {"id": "2025-07-28-131", "parentId": "2025-07-28-111", "teamId": 131, "teamName": "china_test", "teamLevel": 3, "isLeaf": false, "teamParentId": 111, "groupByDate": "2025-07-28"}, {"id": "2025-07-28-1147", "parentId": "2025-07-28--1", "teamId": 1147, "teamName": "ipg_test", "teamLevel": 0, "isLeaf": false, "teamParentId": -1, "groupByDate": "2025-07-28"}, {"id": "2025-07-28-111", "parentId": "2025-07-28-103", "teamId": 111, "teamName": "China-test team", "teamLevel": 2, "isLeaf": false, "teamParentId": 103, "groupByDate": "2025-07-28"}, {"id": "2025-07-28-1151", "parentId": "2025-07-28-1147", "teamId": 1151, "teamName": "ipg_test1", "teamLevel": 1, "isLeaf": false, "teamParentId": 1147, "groupByDate": "2025-07-28"}, {"id": "2025-07-07-774", "parentId": "2025-07-07-1240", "userId": 774, "userName": "sunny yan", "teamId": 1240, "teamName": "419", "isLeaf": true, "teamParentId": 1240, "groupByDate": "2025-07-07", "openings": 33, "talentNum": 0, "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 2, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 6, "apnProNoteNum": 0, "noteCount": 2}, {"id": "2025-07-07-12419", "parentId": "2025-07-07-131", "userId": 12419, "userName": "jack zhang", "teamId": 131, "teamName": "china_test", "isLeaf": true, "teamParentId": 131, "groupByDate": "2025-07-07", "openings": 1, "talentNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-14-12419", "parentId": "2025-07-14-131", "userId": 12419, "userName": "jack zhang", "teamId": 131, "teamName": "china_test", "isLeaf": true, "teamParentId": 131, "groupByDate": "2025-07-14", "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 0, "finalInterviewNum": 0, "interviewNum": 1, "interviewNumProcess": 1, "interviewAppointments": 1, "twoOrMoreInterviews": 0, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 0, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0}, {"id": "2025-06-30-774", "parentId": "2025-06-30-1240", "userId": 774, "userName": "sunny yan", "teamId": 1240, "teamName": "419", "isLeaf": true, "teamParentId": 1240, "groupByDate": "2025-06-30", "openings": 21, "talentNum": 0, "submitToJobNum": 5, "submitToClientNum": 5, "firstInterviewNum": 4, "secondInterviewNum": 1, "finalInterviewNum": 0, "interviewNum": 5, "interviewNumProcess": 4, "interviewAppointments": 5, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 3, "offerNum": 2, "offerAcceptNum": 2, "onboardNum": 2, "eliminateNum": 0, "stayedOver72Hrs": 1, "submitToJobNumAIRecommend": 0, "submitToClientNumAIRecommend": 0, "firstInterviewNumAIRecommend": 0, "secondInterviewNumAIRecommend": 0, "finalInterviewNumAIRecommend": 0, "interviewNumAIRecommend": 0, "twoOrMoreInterviewsAIRecommend": 0, "interviewAppointmentsAIRecommend": 0, "offerNumAIRecommend": 0, "offerAcceptNumAIRecommend": 0, "onboardNumAIRecommend": 0, "eliminateNumAIRecommend": 0, "submitToJobNumPrecisionAIRecommend": 0, "submitToClientNumPrecisionAIRecommend": 0, "firstInterviewNumPrecisionAIRecommend": 0, "secondInterviewNumPrecisionAIRecommend": 0, "finalInterviewNumPrecisionAIRecommend": 0, "interviewNumPrecisionAIRecommend": 0, "twoOrMoreInterviewsPrecisionAIRecommend": 0, "interviewAppointmentsPrecisionAIRecommend": 0, "offerNumPrecisionAIRecommend": 0, "offerAcceptNumPrecisionAIRecommend": 0, "onboardNumPrecisionAIRecommend": 0, "eliminateNumPrecisionAIRecommend": 0, "interviewNumProcessPrecisionAIRecommend": 0, "interviewNumProcessAIRecommend": 0, "callNoteNum": 1, "emailNoteNum": 0, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 0, "createdCompaniesNum": 0, "pipelineNoteNum": 7, "apnProNoteNum": 0, "noteCount": 1}, {"id": "2025-07-28-774", "parentId": "2025-07-28-1240", "userId": 774, "userName": "sunny yan", "teamId": 1240, "teamName": "419", "isLeaf": true, "teamParentId": 1240, "groupByDate": "2025-07-28", "talentNum": 1, "upgradeToClient": 0, "createdCompaniesNum": 0}, {"id": "2025-07-14-774", "parentId": "2025-07-14-1240", "userId": 774, "userName": "sunny yan", "teamId": 1240, "teamName": "419", "isLeaf": true, "teamParentId": 1240, "groupByDate": "2025-07-14", "openings": 21, "talentNum": 3, "submitToJobNum": 1, "submitToClientNum": 1, "firstInterviewNum": 1, "secondInterviewNum": 1, "finalInterviewNum": 1, "interviewNum": 3, "interviewNumProcess": 1, "interviewAppointments": 3, "twoOrMoreInterviews": 1, "uniqueInterviewTalentNum": 1, "offerNum": 1, "offerAcceptNum": 1, "onboardNum": 1, "eliminateNum": 1, "submitToJobNumAIRecommend": 1, "submitToClientNumAIRecommend": 1, "firstInterviewNumAIRecommend": 1, "secondInterviewNumAIRecommend": 1, "finalInterviewNumAIRecommend": 1, "interviewNumAIRecommend": 3, "twoOrMoreInterviewsAIRecommend": 1, "interviewAppointmentsAIRecommend": 3, "offerNumAIRecommend": 1, "offerAcceptNumAIRecommend": 1, "onboardNumAIRecommend": 1, "eliminateNumAIRecommend": 1, "submitToJobNumPrecisionAIRecommend": 1, "submitToClientNumPrecisionAIRecommend": 1, "firstInterviewNumPrecisionAIRecommend": 1, "secondInterviewNumPrecisionAIRecommend": 1, "finalInterviewNumPrecisionAIRecommend": 1, "interviewNumPrecisionAIRecommend": 3, "twoOrMoreInterviewsPrecisionAIRecommend": 1, "interviewAppointmentsPrecisionAIRecommend": 3, "offerNumPrecisionAIRecommend": 1, "offerAcceptNumPrecisionAIRecommend": 1, "onboardNumPrecisionAIRecommend": 1, "eliminateNumPrecisionAIRecommend": 1, "interviewNumProcessPrecisionAIRecommend": 1, "interviewNumProcessAIRecommend": 1, "callNoteNum": 0, "emailNoteNum": 1, "personNoteNum": 0, "videoNoteNum": 0, "otherNoteNum": 0, "iciNum": 0, "upgradeToClient": 2, "createdCompaniesNum": 2, "pipelineNoteNum": 8, "apnProNoteNum": 0, "noteCount": 1}, {"id": "2025-06-30-12419", "parentId": "2025-06-30-131", "userId": 12419, "userName": "jack zhang", "teamId": 131, "teamName": "china_test", "isLeaf": true, "teamParentId": 131, "groupByDate": "2025-06-30"}, {"id": "2025-07-21-774", "parentId": "2025-07-21-1240", "userId": 774, "userName": "sunny yan", "teamId": 1240, "teamName": "419", "isLeaf": true, "teamParentId": 1240, "groupByDate": "2025-07-21"}, {"id": "2025-07-21-12419", "parentId": "2025-07-21-131", "userId": 12419, "userName": "jack zhang", "teamId": 131, "teamName": "china_test", "isLeaf": true, "teamParentId": 131, "groupByDate": "2025-07-21"}, {"id": "2025-07-28-12419", "parentId": "2025-07-28-131", "userId": 12419, "userName": "jack zhang", "teamId": 131, "teamName": "china_test", "isLeaf": true, "teamParentId": 131, "groupByDate": "2025-07-28"}]